<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      round
      :close-on-click-overlay="false"
    >
      <div class="item">
        <div class="top">
          <div class="title">{{ $t('add.title') }}</div>
          <img @click="close" src="@/assets/images/index/close.png" />
        </div>
        <div class="con">
          <div class="input">
            <van-field
              v-model="textarea"
              rows="5"
              type="textarea"
              maxlength="2000"
              show-word-limit
              :placeholder="$t('add.input')"
            />
          </div>
          <div class="topic" v-if="aitList.length">
            <!-- 话题展示 -->
            <div class="list">
              <div
                class="listItem"
                v-for="(item, index) in aitList"
                :key="index"
              >
                @{{ item }}
                <img
                  class="deleteBtn"
                  @click="deleteAit(index)"
                  src="@/assets/images/index/deleteBtn.png"
                />
              </div>
            </div>
          </div>
          <div class="labelMore">
            <div class="imgBtn">
              <img @click="addAit" src="@/assets/images/index/ait.png" />
              <img @click="audio" src="@/assets/images/index/autio.png" />
              <gfile @chooseFile="chooseFile"></gfile>
              <div class="filePrompt">{{ $t('toast.filePrompt') }}</div>
            </div>
            <!-- <div class="num">{{ textarea.length }}/2000</div> -->
          </div>
          <div class="seeFile" v-if="attachmentUrl" @click.stop="openFile">
            <img src="@/assets/images/index/seeFile.png" />
            <div class="name">{{ attachmentName }}</div>
            <img
              @click.stop="fileDelete"
              src="@/assets/images/index/delete.png"
            />
            <!-- <van-button type="primary" @click="openFile('docx')">docx文件</van-button>
                        <van-button type="primary" @click="openFile('pdf')">pdf文件</van-button>
                        <van-button type="primary" @click="openFile('excel')">excel文件</van-button> -->
          </div>
          <AudioRecorder
            :isShowAudio="isShowAudio"
            @dialogCloseAudio="dialogCloseAudio"
            @audioSuccess="audioSuccess"
          ></AudioRecorder>
          <div class="topic">
            <!-- 话题展示 -->
            <div class="list" v-if="topicList.length">
              <div
                class="listItem"
                v-for="(item, index) in topicList"
                :key="index"
              >
                #{{ item }}
                <img
                  class="deleteBtn"
                  @click="deleteTopic(index)"
                  src="@/assets/images/index/deleteBtn.png"
                />
              </div>
            </div>
            <div class="list" v-else>
              <div class="listItem moren">
                {{ $t('add.topic') }}
              </div>
            </div>
            <div class="img">
              <img src="@/assets/images/index/huati.png" @click="addTopic" />
            </div>
          </div>
          <div class="prompt">
            <span class="title">{{ $t('toast.imageTitle') }}</span
            ><span v-if="!type">{{ $t('toast.imageNum') }}</span>
          </div>
          <g-upload
            :mode="imgList"
            :uploadType="type"
            @chooseFile="chooseImage"
            @imgDelete="imgDelete"
            :maxCount="maxCount"
            type="report"
          ></g-upload>
          <div class="prompt">
            <span class="title">{{ $t('toast.videoTitle') }}</span
            >{{ $t('toast.videoLimit') }}{{ $t('toast.videoPrompt') }}
          </div>
          <g-video
            @chooseFile="chooseVideo"
            @videoDelete="videoDelete"
            :maxCount="1"
          ></g-video>
          <div class="whoSee">
            <div class="title">{{ $t('add.whoSee.title') }}</div>
            <van-radio-group v-model="radio" direction="horizontal">
              <van-radio name="0" checked-color="#E46B6B">{{
                $t('add.whoSee.all')
              }}</van-radio>
              <van-radio name="1" checked-color="#E46B6B">{{
                $t('add.whoSee.friend')
              }}</van-radio>
              <!-- :disabled="isDisabled" -->
              <van-radio name="2" checked-color="#E46B6B">{{
                $t('add.whoSee.oneself')
              }}</van-radio>
            </van-radio-group>
          </div>
          <div class="declaration">
            <van-checkbox
              v-model="checked"
              checked-color="#469DF4"
              shape="square"
              label-disabled
              >{{ $t('toast.AgreementFile')
              }}<span
                ><a @click="gotoDetail('/term/serve')">{{
                  $t('toast.fileTitle')
                }}</a></span
              ></van-checkbox
            >
          </div>
          <div class="publish" @click="publish">{{ $t('add.title') }}</div>
        </div>
      </div>
      <Topic
        :dialogFormTopic="dialogFormTopic"
        @dialogCloseTopic="dialogCloseTopic"
        @confirmAdd="confirmAdd"
      >
      </Topic>
      <Ait :dialogFormAit="dialogFormAit" @dialogCloseAit="dialogCloseAit">
      </Ait>
      <div v-if="dialogFormPreview">
        <preview
          :data="attachmentUrl"
          :dialogFormPreview="dialogFormPreview"
          :type="attachmentSuffix"
          @dialogClosePreview="dialogClosePreview"
        ></preview>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import { showToast, closeToast, showLoadingToast } from 'vant'
import gUpload from '@/components/common/g-upload.vue'
import gVideo from '@/components/common/g-video.vue'
import Topic from '@/components/dialog/topic.vue'
import Ait from '@/components/dialog/ait.vue'
import AudioRecorder from '@/components/common/audioRecorder.vue'
import gfile from '@/components/common/g-file.vue'
import preview from '@/components/common/preview.vue'
import emitter from '@/utils/mitt.js'
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import { add, modify } from '@/api/home.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { useRouter } from 'vue-router'
const router = useRouter()
import _ from 'lodash'
const props = defineProps({
  data: {
    type: Object,
    require: false,
    default: {},
  },
  dialogFormAdd: {
    type: Boolean,
    require: false,
    default: false,
  },
  type: {
    type: String,
    require: false,
    default: '',
  },
})
const dialogVisible = ref(false)
const textarea = ref('')
const radio = ref('0')
const imgList = ref([])
const dialogFormTopic = ref(false)
const dialogFormAit = ref(false)
const isShowAudio = ref(false)
const topicList = ref([])
const wangeLength = ref(0)
const aitList = ref([])
const toUserIdList = ref([])
const titleData = ref('')
const imageList = ref([])
const isDisabled = ref(false)
const aitData = ref([])
const dialogFormPreview = ref(false)
const attachmentName = ref('')
const attachmentSize = ref('')
const attachmentSuffix = ref('')
const attachmentUrl = ref('')
const videoUrl = ref('')
const videoFrame = ref('')
const checked = ref(true)
// const maxCount = ref(9)
onMounted(() => {
  console.log('打开add')
  if (props.data.content) {
    // 修改
    // 话题
    topicList.value = [...props.data.tagList]
    // 循环添加
    if (props.data.aitList.length) {
      props.data.aitList.forEach((item) => {
        aitList.value.push(item.name)
        toUserIdList.value.push(item.toUserId)
      })
      // isDisabled.value = true
    }
    // 图片
    const newImageData = []
    props.data.imageList.forEach((item) => {
      const img = { url: item }
      newImageData.push(img)
    })
    console.log(newImageData)
    imgList.value = newImageData
    imageList.value = [...props.data.imageList]
    // 修改成对应格式回显
    // console.log(props.data.msg)
    // 内容
    // emitter.emit('echoSand', props.data.msg)
    textarea.value = props.data.content
    //    谁可以看
    radio.value = props.data.visual
    attachmentName.value = props.data.attachmentName
    attachmentSize.value = props.data.attachmentSize
    attachmentSuffix.value = props.data.attachmentSuffix
    attachmentUrl.value = props.data.attachmentUrl
  }
})

watch(
  () => props.dialogFormAdd,
  (val) => {
    // console.log(val)
    dialogVisible.value = val
  },
  { immediate: true },
)
const maxCount = computed(() => {
  if (props.type) {
    return 1
  } else {
    return 9 // 默认值
  }
})
const gotoDetail = (data) => {
  // 跳转到网页隐私政策页面
  router.push({ path: data })
}
// 点击语音
const audio = () => {
  // window.location.href = "uniwebview://requestpermission";
  //  countStore.audioType = 'add'
  // 判断是web还是app
  if (countStore.deviceType == 'Web') {
    isShowAudio.value = true
  } else {
    window.location.href = 'uniwebview://requestpermission'
    countStore.audioType = 'add'
  }
}
const dialogCloseAudio = (data) => {
  console.log('关闭')
  isShowAudio.value = false
}
// 录音成功
const audioSuccess = (data) => {
  console.log('转文字成功', data)
  textarea.value = textarea.value + data
}
// 定义组件的事件
const emits = defineEmits(['dialogCloseAdd', 'addSuccess'])
const close = () => {
  emits('dialogCloseAdd', false)
  if (props.type) {
    window.location.href = `uniwebview://ImageUploadState?data=1`
    countStore.baseUrl = ''
  }
}
// 上传视频改变
const chooseVideo = (data, posterData) => {
  // console.log(data,posterData)
  videoUrl.value = data
  videoFrame.value = posterData
  emitter.emit('changeImageDisabled', true)
}
// 删除视频
const videoDelete = (data) => {
  console.log(data)
  videoUrl.value = ''
  videoFrame.value = ''
  emitter.emit('changeImageDisabled', false)
}
// 上传图片改变
const chooseImage = (data) => {
  console.log('gaibiangaibian')
  console.log(data)
  // 判断
  imageList.value.push(data)
  console.log(data)
  emitter.emit('changeVideoDisabled', true)
}
const imgDelete = (data) => {
  console.log(data)
  imageList.value.splice(data.index, 1)
  console.log(imageList.value)
  showToast(t('toast.deleteSuccess'))
  if (imageList.value.length == 0) {
    emitter.emit('changeVideoDisabled', false)
  }
}
// 上传文件改变
const chooseFile = (name, size, suffix, url) => {
  console.log(name, size, suffix, url)
  attachmentName.value = name
  attachmentSize.value = size
  attachmentSuffix.value = suffix
  attachmentUrl.value = url
  // console.log(data)
}
const fileDelete = (data) => {
  console.log(data)
  attachmentName.value = ''
  attachmentSize.value = ''
  attachmentSuffix.value = ''
  attachmentUrl.value = ''
}
// 预览文件
const openFile = () => {
  dialogFormPreview.value = true
}
// 关闭预览文件
const dialogClosePreview = () => {
  dialogFormPreview.value = false
}
// 添加话题
const addTopic = () => {
  // 判断是否超过五个
  if (topicList.value.length < 5) {
    dialogFormTopic.value = true
  } else {
    showToast(t('toast.topicInfo'))
  }
}
// 关闭话题
const dialogCloseTopic = () => {
  dialogFormTopic.value = false
}
// 添加话题内容
const confirmAdd = (data) => {
  console.log(data)
  topicList.value.push(data)
  console.log(topicList.value)
}
// 删除话题
const deleteTopic = (index) => {
  topicList.value.splice(index, 1)
}
// 删除@
const deleteAit = (index) => {
  aitList.value.splice(index, 1)
  toUserIdList.value.splice(index, 1)
  aitData.value.splice(index, 1)
}
// 添加@
const addAit = () => {
  if (aitList.value.length < 5) {
    dialogFormAit.value = true
    // emitter.emit('clickAitBtn')
    if (props.data.aitList) {
      // 之前修改动态的逻辑
      console.log('执行aitList')
      console.log(props.data.aitList)
      const data = {
        toUserIdList: toUserIdList.value,
        aitList: props.data.aitList,
      }
      emitter.emit('seleteAit', data)
    } else if (aitData.value) {
      console.log('执行aitData')
      console.log(aitData.value)
      const data = {
        toUserIdList: toUserIdList.value,
        aitList: aitList.value,
        aitData: aitData.value,
      }
      emitter.emit('seleteAit', data)
    } else {
      emitter.emit('seleteAit')
    }

    // if (radio.value == '2') {
    //     // 当前仅自己可见
    //     showToast(t('toast.aitInfoPermission'));
    // } else {
    //     dialogFormAit.value = true
    //     // emitter.emit('clickAitBtn')
    //     emitter.emit('seleteAit', toUserIdList.value)
    // }
  } else {
    showToast(t('toast.aitInfo'))
  }
}
emitter.on('aitSeleteConfirm', (data) => {
  console.log(data)
  aitList.value = data.aitList
  toUserIdList.value = data.toUserIdList
  aitData.value = data.checkoutData
})
emitter.on('openShowAudio', (audioType, data) => {
  if (audioType == 'add') {
    isShowAudio.value = true
  }
})
// 关闭@
const dialogCloseAit = () => {
  dialogFormAit.value = false
}
// 发布
const publish = () => {
  if (props.data.content) {
    // 修改动态
    modifyDynamics()
  } else {
    if (
      textarea.value.length > 0 ||
      imageList.value.length > 0 ||
      videoUrl.value.length > 0
    ) {
      if (checked.value) {
        // 添加动态
        addDynamics()
      } else {
        showToast(t('toast.checkAgreementPrompt'))
      }
    } else {
      // 提示必填项 动态内容不能为空
      showToast(t('toast.dynamicInput'))
    }
  }
}
const addDynamics = _.debounce(
  () => {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    })
    const data = {
      attachmentName: attachmentName.value,
      attachmentSize: attachmentSize.value,
      attachmentSuffix: attachmentSuffix.value,
      attachmentUrl: attachmentUrl.value,
      content: textarea.value,
      imageList: imageList.value,
      tagNameList: topicList.value,
      toUserIdList: toUserIdList.value,
      userId: countStore.loginData.roleId,
      visual: radio.value,
      video: videoUrl.value,
      videoFrame: videoFrame.value,
      imageSource: countStore.baseUrl ? '1' : '2',
    }
    console.log(data)
    add(data)
      .then((res) => {
        console.log(res)
        closeToast()
        if (res.code == 200) {
          showToast(t('toast.publishSuccess'))
          emits('dialogCloseAdd', false)
          emits('addSuccess')
          if (countStore.baseUrl) {
            window.location.href = `uniwebview://ImageUploadState?data=0`
            countStore.baseUrl = ''
          } else {
            window.location.href = `uniwebview://ImageUploadState?data=1`
            countStore.baseUrl = ''
          }
          ta.track('addDynamic', {
            count: 1,
            file_count: attachmentUrl.value.length > 0 ? 1 : 0,
            image_count: imageList.value.length > 0 ? 1 : 0,
            content_count: textarea.value.length > 0 ? 1 : 0,
          })
        } else if (res.code === 500) {
          if (res.msg == '1') {
            showToast({
              message: t('toast.addDynamicViolation'),
            })
          } else if (res.msg == '2') {
            showToast({
              message: t('toast.addTopicViolation'),
            })
          } else if (res.msg == '3') {
            showToast({
              message: t('toast.addImageViolation'),
            })
          } else {
            showToast(t('toast.publishFail'))
          }
        } else {
          showToast(t('toast.publishFail'))
        }
      })
      .catch(function (error) {
        closeToast()
        console.log(error)
        showToast(t('toast.publishFail'))
      })
  },
  1000,
  { leading: true, trailing: false },
)
const modifyDynamics = () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  })
  const data = {
    attachmentName: attachmentName.value,
    attachmentSize: attachmentSize.value,
    attachmentSuffix: attachmentSuffix.value,
    attachmentUrl: attachmentUrl.value,
    content: textarea.value,
    imageList: imageList.value,
    tagNameList: topicList.value,
    toUserIdList: toUserIdList.value,
    userId: countStore.loginData.roleId,
    visual: radio.value,
    id: props.data.id,
    video: videoUrl.value,
    videoFrame: videoFrame.value,
  }
  console.log(data)
  modify(data)
    .then((res) => {
      console.log(res)
      closeToast()
      if (res.code === 200) {
        showToast({
          message: t('toast.modifySuccess'),
        })
        emits('dialogCloseAdd', false)
        emits('addSuccess')
      } else if (res.code === 500) {
        if (res.msg == '1') {
          showToast({
            message: t('toast.addDynamicViolation'),
          })
        } else if (res.msg == '2') {
          showToast({
            message: t('toast.addTopicViolation'),
          })
        } else if (res.msg == '3') {
          showToast({
            message: t('toast.addImageViolation'),
          })
        } else {
          showToast(t('toast.publishFail'))
        }
      } else {
        showToast(t('toast.publishFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      closeToast()
      showToast(t('toast.publishFail'))
    })
}
onBeforeUnmount(() => {
  emitter.off('changeValue')
  emitter.off('openShowAudio')
})
</script>
<style scoped lang="scss">
:deep(.van-popup) {
  width: 90%;
}

:deep(.van-popup--center) {
  max-width: 90%;
}

:deep(.van-cell) {
  background: #f3f3f3;
}

:deep(.van-field__control) {
  font-size: var(--size_26);
  line-height: 37px;
}

.item {
  height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .top {
    height: 80px;
    display: flex;
    font-size: 28px;
    justify-content: center;
    align-items: center;
    padding: 0 24px 0 30px;

    .title {
      margin-top: 30px;
      flex: 1;
      color: var(--mainTtileColor);
      font-size: var(--size_28);
      font-family: JLinXin;
      line-height: 48px;
      font-weight: 550;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .con {
    padding: 0 30px 0;
    position: relative;
    flex: 1;

    .topic {
      display: flex;
      align-items: center;
      margin-bottom: 32px;
      margin-top: 32px;

      .list {
        flex: 1;
        background: #f3f3f3;
        display: flex;
        flex-wrap: wrap;

        .listItem {
          // display: inline-block;
          height: 54px;
          line-height: 54px;
          color: #425e81;
          padding-right: 32px;
          padding-left: 16px;
          position: relative;
          font-size: 30px;

          .deleteBtn {
            position: absolute;
            top: -10px;
            right: 10px;
            width: 26px;
            height: 26px;
          }
        }

        .moren {
          color: #333333;
          font-size: 30px;
        }
      }

      .img {
        width: 54px;
        height: 54px;
        background: #78828e;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 38px;
          height: 38px;
        }
      }
    }

    .labelMore {
      padding-top: 16px;
      display: flex;

      .imgBtn {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: var(--size_20);
        color: var(--bearTextColor);

        img {
          width: 38px;
          height: 38px;
          margin-right: 40px;
        }
      }

      .num {
        font-size: var(--size_24);
        color: var(--bearTtileColor);
      }
    }

    .seeFile {
      margin-top: 16px;
      background: #f3f3f3;
      display: flex;
      align-items: center;
      padding: 0 16px;
      height: 42px;

      img {
        width: 30px;
        height: 30px;
      }

      .name {
        margin-left: 10px;
        flex: 1;
        font-size: var(--size_24);
        color: var(--mainTtileColor);
        white-space: nowrap;
        /* 禁止文本换行 */
        overflow: hidden;
        /* 隐藏超出宽度的文本 */
        text-overflow: ellipsis;
        /* 在文本超出宽度时显示省略号 */
      }
    }

    .prompt {
      margin: 20px 0;
      font-size: var(--size_20);
      color: var(--bearTextColor);
      display: flex;
      align-items: center;

      .title {
        color: var(--mainTtileColor);
        font-weight: 500;
        font-size: var(--size_26);
        padding-right: 30px;
      }
    }

    .whoSee {
      margin-top: 48px;
      display: flex;
      margin-bottom: 24px;

      .title {
        font-weight: 500;
        color: #333333;
        font-size: 24px;
        margin-right: 40px;
      }

      :deep(.van-radio) {
        margin-left: 40px;
      }

      :deep(.van-radio__icon) {
        height: 30px;
        width: 30px;
      }

      :deep(.van-icon) {
        height: 30px;
        width: 30px;
      }

      /* 如果你想改变选中状态下的图标大小 */
      :deep(.van-radio__icon .van-icon) {
        font-size: 25px;
        /* 设置背景大小 */
      }

      :deep(.van-radio__label) {
        color: #333333;
        font-size: 24px;
      }
    }

    .declaration {
      margin: 20px 0;
      display: flex;
      justify-content: center;
      font-size: var(--size_20);
      color: var(--bearTextColor);

      span {
        color: #469df4;
      }

      :deep(.van-checkbox__icon) {
        font-size: 25px;
      }
    }

    .publish {
      position: absolute;
      bottom: 10px;
      right: 28px;
      // width: 152px;
      height: 62px;
      display: flex;
      align-items: center;
      text-align: center;
      color: #c4e5ff;
      font-size: var(--size_28);
      font-weight: var(--weight5);
      background: url('@/assets/images/index/publish.png') no-repeat;
      background-size: 100% 100%;
      padding: 0 30px;
    }
  }
}
</style>
