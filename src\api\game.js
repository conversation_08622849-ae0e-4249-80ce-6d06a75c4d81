import request from '@/axios/axios'
// 获取我的卡牌
export function getMyCard() {
  return request({
    url: `system/card2048User/front/myCard`,
    method: 'GET',
  })
}
// 卡牌list
export function cardList(data) {
  return request({
    url: `system/card2048User/front/list`,
    method: 'POST',
    data   
  })
}
// 商城list
export function shopList() {
  return request({
    url: `system/card2048/front/buy/list`,
    method: 'GET',
  })
}
// 获取余额
export function getBalance(data) {
  return request({
    url: `/system/card2048/front/getCoin`,
    method: 'POST',
    data,
  })
}
// 购买卡片
export function buyCard(data) {
  return request({
    url: `/system/card2048/front/buyCard`,
    method: 'POST',
    data,
  })
}
// 合成卡片
export function mergeCard(data) {
  return request({
    url: `/system/card2048/front/mergeCard`,
    method: 'POST',
    data,
  })
}
// 领取卡片
export function receiveCard(data) {
  return request({
    url: `/system/card2048/front/exchangeCard`,
    method: 'POST',
    data,
  })
}
