import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue'
import postcsspxtoviewport from 'postcss-px-to-viewport'
import viteCompression from 'vite-plugin-compression'
import viteImagemin from 'vite-plugin-imagemin'
export default defineConfig({
  server: {
    host: '0.0.0.0', //"0.0.0.0"network和loaclhost地址都会显示
    open: true, //true项目启动自动打开页面
    proxy: {
      '/api3': {
        target: 'https://fracdn.hapmetasocialltd.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api3/, ''),
      },
      '/api': {
        target: 'http://*************:5000',
        changeOrigin: true, //是否跨域
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/api2': {
        target: 'https://*************:443',
        changeOrigin: true, //是否跨域
        secure: false, //忽略证书
        rewrite: (path) => path.replace(/^\/api2/, ''),
      },
    },
  },
  plugins: [
    vue(),
    viteCompression({
      verbose: true, // 在控制台输出更多信息
      disable: false, // 启用/禁用压缩功能（默认启用）
      deleteOriginFile: false, // 删除原始文件（默认不删除）
      threshold: 10240, // 仅处理大于10KB的文件（字节）
      algorithm: 'gzip', // 使用gzip压缩（默认）或'brotliCompress'使用brotli压缩
      ext: '.gz', // 生成的压缩包后缀（默认.gz）
    }),
    viteImagemin({
      // imagemin的配置项
      gifsicle: {
        optimizationLevel: 7,
        interlaced: false,
      },
      optipng: {
        optimizationLevel: 7,
      },
      mozjpeg: {
        quality: 20,
      },
      pngquant: {
        quality: [0.8, 0.9],
        speed: 4,
      },
      svgo: {
        plugins: [
          {
            name: 'removeViewBox',
          },
          {
            name: 'removeEmptyAttrs',
            active: false,
          },
        ],
      },
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@locales': path.resolve(__dirname, 'src/locales'),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vue: ['vue', 'vue-router', 'pinia'],
          libs: ['lodash-es', 'axios'],
          ui: ['vant', '@vant/touch-emulator'],
        },
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
      },
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  //在这配置插件内容
  css: {
    postcss: {
      plugins: [
        postcsspxtoviewport({
          // 要转化的单位
          unitToConvert: 'px',
          // UI设计稿的大小 测试阶段现用1624
          viewportWidth: 1624,
          // 转换后的精度
          unitPrecision: 6,
          // 转换后的单位
          viewportUnit: 'vw',
          // 字体转换后的单位
          fontViewportUnit: 'vw',
          // 能转换的属性，*表示所有属性，!border表示border不转
          propList: ['*'],
          // 指定不转换为视窗单位的类名，
          selectorBlackList: ['ignore-'],
          // 最小转换的值，小于等于1不转
          minPixelValue: 1,
          // 是否在媒体查询的css代码中也进行转换，默认false
          mediaQuery: true,
          // 是否转换后直接更换属性值
          replace: true,
          // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
          exclude: [],
          // 包含那些文件或者特定文件
          include: [],
          // 是否处理横屏情况
          // landscape: true,
          // landscapeUnit: 'vw', // 横屏时使用的单位
          // landscapeWidth: 1624 // 横屏时使用的视口宽度
        }),
      ],
    },
  },
})
