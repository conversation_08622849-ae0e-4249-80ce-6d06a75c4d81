import axios from 'axios'
import config from '@/utils/env.js'
// 服务器请求地址
// const baseURL = config.explore
// http://192.168.1.106:8808
let request = axios.create({
  baseURL: config.dynamic,
  headers: {
    Authorization: localStorage.getItem('token'),
    // 'Authorization' : 'Bearer eyJ0eXAiOiJhY2Nlc3MiLCJhbGciOiJIUzUxMiJ9.eyJvaWQiOjU3NiwidWlkIjoiMTkwMGI4NDNhYmQzOWZjZDg5MDVlZjA5NDc2NDljYTMiLCJ1c2VyU24iOiJTRzIwMjQwMTMxNTAzMTkyNiIsImV4cCI6MTcyOTMwNzcxNn0.aB5BDSoYk3hsgM__puhGcX709s7dNyyqutctG-uYH1a07Xp-2kKVWDbQLPwHN5N7cWuWZDvQ3MQIt7LR0_ricQ'
  },
  timeout: 20000,
})
// 拦截器的添加
request.interceptors.request.use(
  (config) => {
    if (localStorage.getItem('token')) {
      config.headers['Authorization'] = localStorage.getItem('token') //携带token
    }
    return config
  },
  (err) => {
    return Promise.reject(err)
  },
)
//响应拦截器
request.interceptors.response.use(
  (res) => {
    return res.data
  },
  (err) => {
    return Promise.reject(err)
  },
)
export default request
