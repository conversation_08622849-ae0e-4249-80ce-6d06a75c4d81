const jpn = {
  index: {
    title: 'ホームページ',
    menuItems: {
      Dynamic: '投稿',
      Favorites: '収集',
      Liked: 'いいね',
      Messages: 'メッセージ',
      weChat: 'モーメント',
      myDynamic: '私の投稿',
      myFavorites: 'お気に入り',
      myLiked: '私のいいね',
      Hot: 'ホット',
      Recommend: 'おすすめ',
      New: '最新',
      Vote: '推測&獲得',
      Game: '2048',
    },
    user: {
      praised: 'いいねを獲得する',
      collect: '収集',
      chat: 'チャット',
      addFriends: '友達を追加',
      friendRequest: '友達申請',
      verificationMessage: '認証メッセージ',
      sendNow: '今すぐ送信',
      verificationMessagePlaceholder: 'お元気ですか！お会いできて嬉しいです',
    },
    msg: {
      likeCollectTab: 'いいねとお気に入り',
      commentAitTab: "コメントと{'@'}",
      reply: '返信',
      likeCond: 'あなたの投稿が気に入りました',
      likeConc: 'あなたのコメントが気に入りました',
      likeConr: 'あなたの返信が気に入りました',
      collectCon: 'あなたの投稿を保存しました',
      commentCon: 'あなたの投稿にコメントしました',
      replyCon: '返信しました',
      aitCon: "{'@'}あなた",
    },
  },
  detail: {
    replyNum: '%num% 件の返信があります',
    seeMore: 'もっと見る',
    stop: '折りたたむ',
    originalText: '元のテキスト',
    translation: '翻訳',
    commentNum: '件のコメント',
    send: '送信',
    reply: '返信',
    replied: '返信しました',
    likeListTitle: '%num% 人の友達からの「いいね」',
  },
  add: {
    title: '投稿',
    input: '投稿内容を入力してください',
    topic: 'トピックを追加',
    whoSee: {
      title: '誰に見せることができますか',
      all: '全員',
      friend: '友達が見ることができます',
      oneself: '自分だけに見える',
    },
    publish: 'リリース',
    audioStart: 'クリックして話す',
    audioEnd: 'クリックして終了',
    searchPlaceholder: '友達を検索',
    confirm: '確認',
    audioPermission: 'まずマイクの権限を開いてください',
    imagePermission: 'まずカメラの権限を有効にしてください',
    aitUser: "{'@'}ユーザー",
  },
  report: {
    next: '次のステップ',
    confirmReport: '通報を確認します',
    placeholder: 'コンテンツを入力してください',
    detailTitle: '詳細な説明',
    imgTitle: '画像証拠',
    reportSuccess: '報告成功',
    reportFail: '報告失敗',
    reportSuccessInfo:
      '提出後、プラットフォームは積極的に確認し処理します。社会環境を維持するための努力に感謝します！',
    reportPublish: '戻る',
    reportOneTitleAfterD: 'あなたは%name%の投稿を報告しています',
    reportOneTitleAfterC: '%name%のコメントを報告しています',
    reportOneTitleAfterR: '%name%の返信を報告しています',
    reportTwoTitle: '%title% を選択しました',
    reportEndTitleAfterD: '%name%の投稿に関するレポートは%title%に属しています',
    reportEndTitleAfterC: '%name%のコメントに関するレポートは%title%に属します',
    reportEndTitleAfterR: '%name%の返信に関するレポートは%title%に属します',
  },
  tooltip: {
    delete: '削除',
    modify: '修正',
    cancelCollect: 'お気に入り解除',
    report: '通報',
    block: 'ブロックする',
  },
  delete: {
    deleteCon: 'このコンテンツを削除してもよろしいですか？',
    deleteCancel: 'キャンセル',
    deleteConfirm: '確認',
    blockCon: 'ブロックしてもよろしいですか？',
  },
  toast: {
    likeSuccess: '成功のように',
    likeCancel: 'いいねがキャンセルされました',
    likeFail: 'いいねに失敗しました',
    collectSuccess: 'お気に入り成功',
    collectCancel: 'お気に入り解除しました',
    collectFail: 'お気に入りに失敗しました',
    publishSuccess: '投稿成功',
    publishFail: '投稿に失敗しました',
    modifySuccess: '修正成功',
    topicInfo: '最大5つのトピックを選択できます',
    aitInfo: "最大{'@'}5人のユーザー",
    ait: "{'@'}の前に少なくとも1文字を入力してください",
    dynamicInput:
      '動的コンテンツ、画像、動画のうち、少なくとも1つをアップロードしてください',
    nextInfo: '最初に1つのオプションを選択してください',
    reportSuccess: '報告成功',
    reportFail: '報告失敗',
    audioTextSuccess: '音声からテキストへの変換に成功しました',
    audioTextFail: '音声からテキストへの変換に失敗しました',
    translationSuccess: '翻訳に成功しました',
    translationFail: '翻訳に失敗しました',
    uploadImageFail: 'アップロードに失敗しました',
    deleteSuccess: '削除に成功しました ',
    deleteFail: '削除に失敗しました',
    // 新加
    imageLimit: 'ファイルサイズは%num%MBを超えることはできません',
    imageNum:
      '最大9枚の画像をアップロードできます9枚の画像をアップロードできます',
    uploadPrompt: '画像&動画のアップロードはこちら',
    filePrompt:
      '（Doc、docx、PDFファイル形式がサポートされており、ファイルサイズは5MBを超えてはなりません）',
    imageBefore: '1枚の画像で4MB以下',
    imageShowToast: 'アップロードファイルが大きすぎます',
    audioFail: '録音の終了エラー',
    collectCancelFail: 'キャンセルに失敗しました  ',
    collectCancelSuccess: 'キャンセルに成功しました ',
    dynamicFail: '投稿は存在しません',
    addCommentViolation:
      '提出された内容は規則に違反している疑いがありますので、修正して再提出してください。修正して再提出してください。',
    addCommentFail: 'コメントの追加に失敗しました',
    addReplyFail: '返信の追加に失敗しました',
    addDynamicViolation:
      '提出された「投稿」内容は規則に違反している疑いがありますので、修正して再提出してください。',
    addTopicViolation:
      'あなたが提出した「トピック」の内容は、規則に違反している疑いがありますので、修正して再提出してください。',
    addImageViolation:
      '提出された「画像」コンテンツは規制に違反している疑いがありますので、修正して再提出してください。',
    topicCon: 'トピックの内容は空にできません',
    getMsgFail: '情報の取得に失敗しました',
    loginFail: 'ログインに失敗しました',
    aitInfoPermission: '現在は自分だけに表示されています',
    alreadyReport:
      '複数回通報しましたので、プラットフォームからのフィードバックをお待ちください',
    commentAfterDelete: 'コメントが削除されました',
    replyAfterDelete: '返信が削除されました',
    msgDataListFail: 'データ取得に失敗しました',
    videoLimit: '動画は最大25Mまで',
    videoPrompt: '最大1つのビデオのアップロード',
    videoToast: '画像や動画しかアップロードできません',
    imageTitle: '画像のアップロード',
    videoTitle: 'ビデオのアップロード',
    applySuccess: '友達申請が成功しました',
    applyFail: '友達申請の送信に失敗しました',
    blacklistPrompt: 'ブラックリストに登録されているため、友達を追加できません',
    friendNumPrompt: '相手の友達リストが満員です',
    myNumPrompt: 'あなたの友達リストが満員です',
    failedPrompt: 'パラメーターエラー',
    alignPrompt: 'すでに友達として追加されているため、再度申請できません',
    applyMyPrompt: '自分自身を追加することはできません',
    alignApply: '友達申請はすでに送信済みです。48時間後に再度申請できます',
    blockSuccess: 'ユーザーをブラックリストに追加しました',
    blockFail: 'ブロックに失敗しました。',
    blockListFull: 'ブロックリストがいっぱいです',
    checkAgreementPrompt:
      'あなたは《コンテンツ発表声明》に同意していないので、動態を発表できません',
    AgreementFile: 'その文書を読み、同意しました',
    fileTitle: '《コンテンツ発表声明》',
    sameLanguagePrompt: '現在は同じ言語を使用しているため、翻訳は不要です',
  },
  vote: {
    voteProgress: '進行中',
    voteEnd: '終了しました',
    voteSettle: '決済済み',
    oneselfNum: '投票済み',
    voteNum: '{num}コイン',
    timeName: '残りの時間 ',
    allNum: '総コイン数',
    participateInVoting: '参加者数:',
    getCoins: '今回は、{num} コインを獲得しました',
    voteBtn: '選ぶ ',
    voteTitle: '{num}に投票する',
    inputInfo: '数量を選んでください',
    voteConfirm: '確認',
    voteSuccess: '成功',
    voteFail: '失敗',
    statusEnd: 'イベントは終了しました',
    voteTnfo: 'イベントに参加するための最少コイン数は1です',
    hold: '持有',
    balance: '現在のアカウント残高が不足しています。すぐにチャージしてください',
    remainingTimeData: '{days}日{hours}時{minutes}分{seconds}秒',
    remainingTimeDaysHours: '{days}日{hours}時',
    questionInfo:
      'ユーザーのすべてのコインは、このイベントの賞金プールに入り、正しい予想をしたユーザーは、予想したコインの数に応じて賞金プール内のすべての【Time-space coins】を分割します。',
  },
  video: {
    videoIndex: '何か言って... ',
    videoDetail: '詳細ページ',
    videoTitle: '動画',
  },
  match: {
    statusProgress: '進行中',
    statusEnd: '終了しました',
    matchCon: 'コンテストの内容',
    matchRule: 'コンテストのルール ',
    matchAward: 'コンテストの報酬 ',
    matchInstructions: 'コンペティションガイドライン',
    matchWorks: '作品',
    matchTitle: 'コンテスト ',
    matchTime: '応募期間',
    myMatch: '私のコンテスト',
    statusWaiting: '開始を待つ',
    vote: '投票',
    voted: '投票済み',
    voteNum: '{num}票',
    uploadWorks: '作品をアップロードする',
    matchAddTitle: 'タイトル',
    titlePlaceholder: 'タイトルを入力してください',
    submit: '提出',
    content: '内容',
    voteConfirm: '{name}の作品に投票しますか？',
    voteSuccess: '投票に成功しました',
    voteFail: '投票が失敗しました ',
    imageShowToast: 'タイトルと画像は空にできません',
    videoShowToast: 'タイトルとビデオは空にできません',
    addWorkTitle:
      '提出された「タイトル」の内容は、規則違反が疑われています。変更後、再度提出してください。',
    addWorkContent:
      '提出された「内容」の内容は、規則違反が疑われています。変更後、再度提出してください。',
    againAddWork:
      '現在のコンテストにすでに参加しているため、再度の参加はできません！',
    myMatchTitle: '私の作品',
    otherMatchTitle: '他人の作品',
  },
  empty: {
    comment: 'まだコメントはありません',
    list: 'データは利用できません',
    content: '利用可能なコンテンツはありません',
    message: 'メッセージは利用できません',
  },
  game:{
    myCardTitle:`オレのカード`,
    merge:`合成`,
    maskObtaining:`优胜おめでとう`,
    maskInfo:`急いで新しいカードを合成するために友達を追加してください!`,
    clickAnywhere:`どこかをクリックして続けます`,
    mergeSuccess:`合成成功`,
    newFriends:`新しい友達`,
    receiveTitle:`おめでとうございます！`,
    receiveInfo:`請求が成功すると、2048は消えます!`,
    receiveImgText:`テレポートカード`,
    receivePublish:`郵便受けに行って報酬をもらう`,
    shop:`モール`,
    purchaseCards:`購入カード`,
    totalAmount:`総額`,
    purchase:`購入`,
    selectCards:`選択カード`,
    purchaseSuccessful:`購入成功`,
    purchaseFailed:`購入失敗した`,
    synthesisFailed:`合成失敗`,
    claimSuccessful:`請求成功`,
    claimFailed:`請求項失敗`,
    exchangeTips:`2048カードを持っています。テレポートカードで買い換えましょう！`,
    rule:`ゲームのルール`,
    ruleNum1:`1.ゲームを開始すると、ランダムに2色の基本カードを獲得します`,
    ruleNum2:`2.同じ数字、異なる色のカードを持つ他のユーザーと友達を追加することで、より高級なカードを合成することができる`,
    ruleNum3:`3.合成に成功すると、2つのアカウントの元の数字のカードが2倍の数字の新しいカードにアップグレードされる`,
    ruleNum4:`4.カードの数字が2048に達したら、転送カードを1枚交換することができます`,
    ruleNum5:`5.合成に失敗した理由は次のとおりです。アップグレードしたカードの色を相手と同じ色に選択した場合、カードはすでに他の人に使われている。あなたと相手はすでに親友です。友達の数が上限に達しています。相手の友だちの数が上限になるなど。`
  }
}

export default jpn
