<template>
  <div class="container">
    <div class="top">
      <img class="back" @click="back" src="@/assets/images/game/back.webp" />
      <div class="title">{{ $t('game.shop') }}</div>
      <img class="question" src="@/assets/images/game/question.webp" @click="handleGameRule" alt="问号" />
      <div class="coin">
        <img src="@/assets/images/vote/glod.webp" alt="金币" />
        <div class="coin-num">{{ coinNum }}</div>
      </div>
    </div>
    <div class="content">
      <div class="goods-list">
        <div class="goods-item" v-for="item in goodsList" :key="item.id" @click="openBuyCord(item)">
          <div class="goods-item-card">
            <GameCard :color="item.cardColor" :number="item.cardNumber" cardType="shop" />
          </div>
          <div class="coin-price">
            <img src="@/assets/images/vote/glod.webp" alt="金币" />
            <div class="coin-num">{{ item.cardPrice }}</div>
          </div>
        </div>
      </div>
    </div>
    <Rule :dialogGameRule="dialogGameRule" @dialogClose="handleDialogClose" />
    <buyCord :dialogbuyCord="dialogbuyCord" :buyCordData="buyCordData" :coinNum="coinNum"
      @dialogClose="handleDialogBuyClose" @buySuccess="buySuccess" />
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import emitter from '@/utils/mitt.js'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const route = useRoute()
const router = useRouter()
import GameCard from '@/components/common/GameCard.vue'
import Rule from '@/components/game/rule.vue'
import buyCord from '@/components/game/buyCord.vue'
import { shopList, getBalance } from '@/api/game.js'
const dialogGameRule = ref(false)
const dialogbuyCord = ref(false)
const buyCordData = ref({})
const coinNum = ref(0)
onMounted(() => {
  console.log('shop页面挂载')
})
const goodsList = ref([])

const back = () => {
  router.go(-1)
  emitter.emit('homeBack')
}

const handleGameRule = () => {
  dialogGameRule.value = true
}
const handleDialogClose = () => {
  dialogGameRule.value = false
}
const openBuyCord = (item) => {
  dialogbuyCord.value = true
  buyCordData.value = item
}
const handleDialogBuyClose = () => {
  dialogbuyCord.value = false
}
// 购买成功重新获取余额
const buySuccess = () => {
  getBalanceDdata()
  // 通知2048首页更新我的卡牌
  emitter.emit('buyCardsSuccess')
}
const getShopList = () => {
  shopList()
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        goodsList.value = res.data
      } else {
        showToast(t('toast.getMsgFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.getMsgFail'))
    })
}
const getBalanceDdata = () => {
  const query = {
    token: countStore.loginData.token,
  }
  getBalance(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        coinNum.value = res.data
      } else {
        showToast(t('toast.getMsgFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.getMsgFail'))
    })
}

onMounted(() => {
  // 获取商城cardlist
  getShopList()
  // 获取余额
  getBalanceDdata()
})
onUnmounted(() => { })
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/images/game/shop-bg.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .top {
    display: flex;
    padding: 27px 328px 23px 30px;

    .back {
      width: 44px;
      height: 44px;
      margin-right: 28px;
    }

    .title {
      flex: 1;
      font-size: 30px;
      color: #2e4862;
      font-weight: 500;
    }

    .question {
      width: 46px;
      height: 46px;
      margin-right: 40px;
    }

    .coin {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      img {
        width: 46px;
        height: 46px;
      }

      .coin-num {
        position: absolute;
        left: 23px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 22px;
        color: rgba(255, 255, 255, 0.8);
        height: 38px;
        background: rgba(27, 41, 50, 0.2);
        border-radius: 0px 19px 19px 0px;
        line-height: 38px;
        padding: 0 60px 0 20px;
      }
    }
  }

  .content {
    flex: 1;
    width: 976px;
    margin: 0 auto 62px;
    background: url('@/assets/images/game/store_icon_bg.webp') no-repeat center center;
    background-size: 100% 100%;
    padding: 16px;

    .goods-list {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      .goods-item {
        width: 100%;
        height: 100%;
        background: url('@/assets/images/game/plug_btm.webp') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .goods-item-card {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .coin-price {
          width: 100%;
          height: 48px;
          background: url('@/assets/images/game/coin_blue_bar.webp') no-repeat center center;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 30px;

          img {
            width: 40px;
            height: 40px;
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>
