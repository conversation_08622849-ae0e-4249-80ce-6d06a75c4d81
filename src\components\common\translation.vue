<template>
  <span
    v-if="istranslation"
    class="translation"
    @click.stop="changeTranslation"
  >
    {{ $t('detail.originalText') }}
  </span>
  <span v-else class="translation" @click.stop="changeTranslation">
    {{ $t('detail.translation') }}
  </span>
</template>
<script setup>
import { ref } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { translation, axiosCancel } from '@/api/translation.js'
import axios from 'axios'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import { getLanguage } from '@/utils/language.js'
const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  language: {
    type: String,
    default: '',
  },
})
const istranslation = ref(false)
const emits = defineEmits(['changeTranslationCon'])
// 点击翻译
const changeTranslation = () => {
  // console.log(countStore.language) //界面语言
  if (istranslation.value) {
    // 原文
    console.log('原文')
    emits('changeTranslationCon', props.content)
    axiosCancel()
    istranslation.value = false
  } else {
    console.log(countStore.userLanguage)
    console.log(props.language)
    if (countStore.userLanguage == props.language) {
      istranslation.value = true
      showToast(t('toast.sameLanguagePrompt'))
    } else {
      // 翻译
      console.log('翻译')
      showLoadingToast({
        forbidClick: true,
        duration: 0,
      })
      const query = {
        UserId: countStore.loginData.avatarId,
        content: props.content,
        token: countStore.apiToken,
        language: getLanguage(countStore.userLanguage),
      }
      translation(query)
        .then((res) => {
          // 接口调用成功之后的操作
          console.log(res)
          closeToast()
          if (res.result) {
            istranslation.value = true
            emits('changeTranslationCon', res.result)
            showToast(t('toast.translationSuccess'))
          } else {
            showToast(t('toast.translationFail'))
          }
        })
        .catch((err) => {
          // closeToast();
          // 接口调用失败之后的操作
          if (axios.isCancel(err)) {
            console.log('Request canceled', err.message)
          } else {
            console.log(err)
            showToast(t('toast.translationFail'))
          }
        })
    }
  }
}
</script>
