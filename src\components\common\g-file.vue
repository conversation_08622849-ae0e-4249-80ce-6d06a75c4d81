<template>
  <div class="upload">
    <!-- accept="file" -->
    <van-uploader
      :after-read="afterRead"
      :before-read="beforeRead"
      accept=".pdf,.docx,.doc"
      :max-count="maxCount"
      @delete="deleteFile"
    >
      <img src="@/assets/images/index/file.png" />
    </van-uploader>
    <van-overlay :show="showOverlay" />
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { uploadFile } from '@/api/home.js'
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const props = defineProps({
  //上传最大数量
  maxCount: {
    type: Number,
    default: 1,
  },
})
const fileList = ref([])
const showOverlay = ref(false)
onMounted(() => {})
const emits = defineEmits(['chooseFile', 'imgDelete'])
const deleteFile = (file, index) => {
  console.log(index)
  console.log(file, index.index)
  // 删除文件逻辑，可以根据需要进行服务器删除等操
  console.log(fileList.value)
  emits('imgDelete', index)
  // fileList.value.splice(index, 1);
}
// 文件读取前的钩子，返回 false 可以阻止文件读取
const beforeRead = (file) => {
  console.log('文件选择之前')
  // 在这里可以进行文件格式、大小的校验
  if (file.size > 1024 * 1024 * 5) {
    const str = t('toast.imageLimit')
    const newStr = str.replace('%num%', 5)
    showToast(newStr)
    return false
  }
  // 返回 true 允许读取文件
  return true
}
// 文件读取完成后的钩子
const afterRead = (file, index) => {
  // console.log(file,index)
  // 示例使用 axios 进行文件上传
  upload(file, index)
}
// 上传文件的方法
const upload = (file, index) => {
  console.log('开始上传')
  showOverlay.value = true
  const formData = new FormData()
  formData.append('file', file.file)
  // console.log(formData)
  uploadFile(formData)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code == 200) {
        // this.imageDialogVisible = false;
        // this.$emit('successImage', res.data)
        emits('chooseFile', res.name, res.size, res.suffix, res.url)
        showOverlay.value = false
      } else {
        showToast(t('toast.uploadImageFail'))
        fileList.value.splice(index.index, 1)
        showOverlay.value = false
      }
    })
    .catch((err) => {
      // 接口调用失败之后的操作
      console.log(err)
      showToast(t('toast.uploadImageFail'))
      showOverlay.value = false
    })
}
onUnmounted(() => {})
</script>

<style scoped lang="scss">
.upload {
  width: 38px;
  height: 38px;
  position: relative;

  img {
    width: 38px;
    height: 38px;
  }
}

.van-uploader {
  width: 38px;
  height: 38px;
  position: absolute;
  top: 0;
  bottom: 0;
}

:deep(.van-uploader__wrapper) {
  font-size: var(--size_16);
}
</style>
