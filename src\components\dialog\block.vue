<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      title=""
      show-cancel-button
      @close="handleClose"
      @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay"
    >
      <div class="deleteCon">
        <div class="title">{{ $t('delete.blockCon') }}</div>
        <div class="buttom">
          <div class="buttomBtn" @click="handleCancel">
            {{ $t('delete.deleteCancel') }}
          </div>
          <div class="buttomBtn activeBtn" @click="handleConfirm">
            {{ $t('delete.deleteConfirm') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
const dialogVisible = ref(false)
const props = defineProps({
  dialogBlock: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogBlock,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClose', 'confirm'])
// 确认按钮
const handleConfirm = () => {
  emits('dialogClose')
  emits('confirm')
}
// 取消按钮
const handleCancel = () => {
  emits('dialogClose')
}
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const onClickPopup = () => {}
const onClickOverlay = () => {}
</script>
<style lang="scss" scoped>
@import '@/assets/css/common/deleteMsg.scss';
</style>
