<template>
  <div class="avatar" :class="[{ backgroundImage: isImageLoaded }, className]">
    <img @click="gotoDetail" :src="url" alt="" @load="imageLoaded" />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
import emitter from '@/utils/mitt.js'
const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  userId: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
})
const isImageLoaded = ref(false)
// 图片加载完成
const imageLoaded = () => {
  // console.log('图片加载完成');
  isImageLoaded.value = true
}
const gotoDetail = () => {
  // router.push({ path: '/reportOne', query: reportData })
  if (route.path === '/home' || props.type == 'msg') {
    // 如果路径相同，则不进行任何操作
    console.log('路径相同')
  } else {
    // 否则，导航到指定路径
    const query = {
      userId: props.userId,
    }
    console.log('ssd')
    emitter.emit('closeDetail')
    router.push({ path: '/home', query })
  }
}
</script>
<style lang="scss" scoped>
.avatar {
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 68px;
    /* 头像宽度 */
    height: 68px;
    /* 头像高度自动调整 */
  }
}

.friendRequest {
  width: 108px;
  height: 108px;

  img {
    width: 94px;
    /* 头像宽度 */
    height: 94px;
  }
}

.comment {
  width: 60px;
  height: 60px;

  img {
    width: 50px;
    /* 头像宽度 */
    height: 50px;
  }
}

.reply {
  width: 40px;
  height: 40px;

  img {
    width: 32px;
    /* 头像宽度 */
    height: 32px;
  }
}

.backgroundImage {
  background: url('@/assets/images/index/avatarBg.webp') no-repeat;
  background-size: 100% 100%;
}
</style>
