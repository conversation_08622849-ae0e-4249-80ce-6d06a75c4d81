<template>
  <div>
    <div class="item" @click.stop="clickReply">
      <div class="left">
        <avatar
          :url="data.avatart"
          :userId="data.userId"
          className="reply"
        ></avatar>
      </div>
      <div class="right">
        <div class="info">
          <div class="name">
            {{ data.userName }} {{ $t('detail.replied') }} {{ data.toUserName }}
          </div>
          <div class="msg">{{ translationCon }}</div>
          <img
            v-if="data.images"
            :src="data.images"
            @click.stop="handleClick(data.images)"
            alt=""
          />
        </div>
        <div class="btn">
          <div class="time">
            {{ time(data.createTime) }}
            <translation
              v-if="data.content.trim() && !isOnlyEmoji(data.content)"
              :content="data.content"
              :language="data.language"
              @changeTranslationCon="changeTranslationCon"
            ></translation>
          </div>
          <div class="bottomItem">
            <img :src="imgLoveUrl" @click.stop="changeLike(data.id)" />
            <div>{{ data.likeCount }}</div>
          </div>
          <popover
            :data="data"
            :commentId="commentId"
            tooltipType="replyReport"
            @changeReply="changeReply"
          ></popover>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { showToast } from 'vant'
const avatar = defineAsyncComponent(
  () => import('@/components/common/avatar.vue'),
)
import emitter from '@/utils/mitt.js'
import { ymdDianTime, isOnlyEmoji } from '@/utils/time.js'
import { isSeleteDetailLikeComment } from '@/assets/js/select.js'
import { like } from '@/api/home.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import translation from '@/components/common/translation.vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    },
  },
  commentId: {
    type: String,
    default: '',
  },
})
const imgLoveUrl = ref()
const translationCon = ref('')
// 计算时间
const time = (data) => {
  return ymdDianTime(data)
}
onMounted(() => {
  translationCon.value = props.data.content
  props.data.isLike
    ? (imgLoveUrl.value = isSeleteDetailLikeComment.selete)
    : (imgLoveUrl.value = isSeleteDetailLikeComment.noselete)
})
const handleClick = (data) => {
  showImagePreview({
    images: [data],
  })
}
//获取到翻译组件传得值
const changeTranslationCon = (data) => {
  translationCon.value = data
}
// 点击点赞
const changeLike = (id) => {
  const query = {
    dyNamicsId: id,
    likeType: '2',
  }
  like(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        console.log(props.data.isLike)
        if (props.data.isLike) {
          props.data.likeCount--
          props.data.isLike = 0
          showToast(t('toast.likeCancel'))
          imgLoveUrl.value = isSeleteDetailLikeComment.noselete
        } else {
          props.data.likeCount++
          props.data.isLike = 1
          showToast(t('toast.likeSuccess'))
          imgLoveUrl.value = isSeleteDetailLikeComment.selete
        }
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.likeFail'))
    })
}
// 定义组件的事件
const emits = defineEmits(['getReplyFocus', 'deleteRelpyList'])
// 点击回复
const clickReply = () => {
  console.log('传第四')
  // 向祖组件传递获取焦点事件
  // emitter.emit('getReplyFocus')
  emits('getReplyFocus', props.data.id, props.data.userId, props.data.userName)
}
// 像评论传递更新回复列表
const changeReply = (data) => {
  emits('deleteRelpyList', data)
}
onUnmounted(() => {})
</script>

<style scoped lang="scss">
@import '@/assets/css/detail/commentItem.scss';

.item {
  .right {
    border-bottom: none;
  }
}
</style>
