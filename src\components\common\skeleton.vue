<template>
  <div class="skeleton">
    <van-skeleton v-for="(item, index) in 6" :key="index">
      <template #template>
        <div :style="{ display: 'flex', width: '100%' }">
          <van-skeleton-image />
          <div :style="{ flex: 1, marginLeft: '16px' }">
            <van-skeleton-paragraph />
            <van-skeleton-paragraph row-width="80%" />
            <van-skeleton-paragraph row-width="70%" />
            <van-skeleton-paragraph row-width="60%" />
          </div>
        </div>
      </template>
    </van-skeleton>
  </div>
</template>
<script></script>
<style scoped lang="scss">
.skeleton {
  display: grid;
  grid-template-columns: repeat(2, 1fr);

  .van-skeleton {
    margin: 50px 0;
  }

  .van-skeleton-paragraph {
    background-color: #e2e4e7;
    height: 30px;
    margin-bottom: 20px;
  }
}
</style>
