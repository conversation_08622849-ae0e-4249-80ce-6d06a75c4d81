<template>
  <div>
    <van-popup v-model:show="dialogVisible" title="" show-cancel-button @close="handleClose" @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay">
      <div class="deleteCon">
        <div class="title">{{ $t('game.purchaseCards') }}</div>
        <div class="con">
          <GameCard :color="buyCordData.cardColor" :number="buyCordData.cardNumber" cardType="buyToast" />
          <div class="numberCon">
            <img @click="reduceNumber" src="@/assets/images/vote/reduceNumber.webp" />
            <div class="number">{{ buyNumber }}</div>
            <img @click="addNumber" src="@/assets/images/vote/addNumber.webp" />
          </div>
          <div class="price">
            {{ $t('game.totalAmount') }}:<img src="@/assets/images/vote/glod.webp" alt="金币" />{{ buyCordData.cardPrice *
              buyNumber }}
          </div>
          <div class="balance">
            <img src="@/assets/images/vote/glod.webp" alt="金币" />
            <div class="coin-num">{{ coinNum }}</div>
          </div>
        </div>
        <div class="buttom">
          <div class="buttomBtn" @click="handleCancel">
            {{ $t('delete.deleteCancel') }}
          </div>
          <div class="buttomBtn activeBtn" @click="handleConfirm">
            {{ $t('game.purchase') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import GameCard from '@/components/common/GameCard.vue'
import { buyCard } from '@/api/game.js'
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const dialogVisible = ref(false)
const props = defineProps({
  dialogbuyCord: {
    type: Boolean,
    require: false,
    default: false,
  },
  buyCordData: {
    type: Object,
    require: false,
    default: () => ({}),
  },
  coinNum: {
    type: Number,
    require: false,
    default: '',
  },
})
const buyNumber = ref(1)
watch(
  () => props.dialogbuyCord,
  (val) => {
    // console.log(val)
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClose', 'buySuccess'])
// 确认按钮
const handleConfirm = () => {
  const query = {
    id: props.buyCordData.id,
    coin: props.buyCordData.cardPrice,
    number: buyNumber.value,
    token: countStore.loginData.token,
  }
  buyCard(query)
    .then((res) => {
      console.log(res)
      if (res.code == 200) {
        emits('dialogClose')
        emits('buySuccess')
        showToast(t('game.purchaseSuccessful'))
      } else {
        showToast(t('game.purchaseFailed'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('game.purchaseFailed'))
    })
}
// 取消按钮
const handleCancel = () => {
  emits('dialogClose')
}
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const onClickPopup = () => { }
const onClickOverlay = () => { }
const addNumber = () => {
  buyNumber.value++
}
const reduceNumber = () => {
  if (buyNumber.value > 1) {
    buyNumber.value--
  }
}
</script>
<style lang="scss" scoped>
// @import '@/assets/css/common/deleteMsg.scss';
:deep(.van-popup) {
  background: none;
}

.deleteCon {
  width: 628px;
  background: url('@/assets/images/game/shopBg.webp') no-repeat center center;
  background-size: 100% 100%;
  padding: 0 40px;

  .title {
    font-size: 30px;
    color: #224c8b;
    font-weight: bold;
    text-align: center;
    height: 80px;
    line-height: 80px;
  }

  .con {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 20px;

    .numberCon {
      margin-top: 24px;
      display: flex;
      align-items: center;

      .number {
        padding: 0 40px;
      }
    }

    .price {
      margin: 14px 0 8px 0;
      display: flex;
      align-items: center;
      font-size: 24px;
      color: rgba(57, 57, 57, 0.78);

      img {
        width: 38px;
        height: 38px;
        margin-right: 10px;
      }
    }

    .balance {
      position: absolute;
      top: 0;
      right: 20px;
      display: flex;
      align-items: center;

      img {
        width: 38px;
        height: 38px;
      }
    }
  }

  .buttom {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    font-size: 30px;
    margin: 10px 0;

    .buttomBtn {
      width: 100%;
      height: 92px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      color: #222222;
      text-align: center;
      background: url('@/assets/images/game/cancel.webp') no-repeat;
      background-size: 100% 100%;
    }

    .activeBtn {
      color: #ffffff;
      background: url('@/assets/images/game/shop-Btn.webp') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
