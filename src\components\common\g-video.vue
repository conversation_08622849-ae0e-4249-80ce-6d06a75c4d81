<template>
  <div class="upload">
    <van-uploader
      v-model="fileList"
      ref="uploadImg"
      :max-size="25 * 1024 * 1024"
      @oversize="onOversize"
      :after-read="afterRead"
      :before-read="beforeRead"
      accept="video/mp4, video/x-matroska, video/quicktime, video/webm"
      :max-count="maxCount"
      :disabled="isPermossion"
      @delete="deleteFile"
    >
      <template #preview-cover>
        <videoPlay
          className="videoWH"
          :data="videoUrl"
          :imageData="videoFrame"
        ></videoPlay>
      </template>
      <div class="img">
        <span v-if="showMask" @click="handleClick" class="message"></span>
        <img src="@/assets/images/index/addVideo.webp" alt="" />
      </div>
    </van-uploader>
    <van-overlay :show="showOverlay" />
  </div>
</template>

<script setup>
import {
  ref,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  defineAsyncComponent,
} from 'vue'
const videoPlay = defineAsyncComponent(
  () => import('@/components/common/video.vue'),
)
import { showToast } from 'vant'
import { uploadVideo } from '@/api/home.js'
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const props = defineProps({
  //上传最大数量
  maxCount: {
    type: Number,
    default: 1,
  },
})
const fileList = ref([])
const videoUrl = ref('')
const videoFrame = ref('')
const showOverlay = ref(false)
const isDisabled = ref(false)
const showVideo = ref(false)
const isPermossion = ref(true)
const showMask = ref(true)
const uploadImg = ref(null)
onMounted(() => {
  if (countStore.deviceType == 'Web') {
    isPermossion.value = false
  } else {
    if (countStore.isCamera) {
      //    有权限
      isPermossion.value = false
    } else {
      window.location.href = 'uniwebview://checkCameraPermission'
    }
  }
})
watch(
  () => countStore.isCamera,
  (val) => {
    // console.log(val)
    isPermossion.value = !countStore.isCamera
  },
  { immediate: true },
)
const handleClick = () => {
  console.log('sss')
  // showToast('相机权限' + isPermossion.value)
  if (isPermossion.value) {
    // 先判断是否有权限 开启权限
    window.location.href = 'uniwebview://requestCameraPerssion'
  } else {
    if (showOverlay.value) {
      console.log('hahhahhh')
    } else {
      nextTick(() => {
        // showToast('成功')
        uploadImg.value.chooseFile()
      })
    }
  }
}
emitter.on('isUploadVideo', (data) => {
  if (data == 'true') {
    isPermossion.value = false
    nextTick(() => {
      showToast(t('toast.uploadPrompt'))
    })
  }
})
emitter.on('changeVideoDisabled', (data) => {
  console.log(data)
  isDisabled.value = data
})
const emits = defineEmits(['chooseFile', 'videoDelete'])
const deleteFile = (file, index) => {
  console.log(index)
  console.log(file, index.index)
  // 删除文件逻辑，可以根据需要进行服务器删除等操
  console.log(fileList.value)
  emits('videoDelete', index)
  fileList.value.splice(index, 1)
  videoUrl.value = ''
  videoFrame.value = ''
}
const onOversize = (file) => {
  showToast(t('toast.videoLimit'))
}
// 文件读取前的钩子，返回 false 可以阻止文件读取
const beforeRead = (file) => {
  // console.log(file)
  // showToast(file.name)
  if (isDisabled.value) {
    showToast(t('toast.videoToast'))
    return false
  } else {
    // 返回 true 允许读取文件
    return true
  }
}
// 文件读取完成后的钩子
const afterRead = (file, index) => {
  // console.log(file)
  // 生成视频预览 URL
  file.objectUrl = URL.createObjectURL(file.file)
  console.log('Generated URL:', file.objectUrl) // 打印 URL 确认生成成功
  videoUrl.value = file.objectUrl
  // console.log(file,index)
  // 示例使用 axios 进行文件上传
  file.status = 'uploading'
  upload(file, index)
}
// 上传文件的方法
const upload = (file, index) => {
  console.log('开始上传')
  showOverlay.value = true
  const formData = new FormData()
  formData.append('file', file.file)
  // console.log(formData)
  uploadVideo(formData)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code == 200) {
        // this.imageDialogVisible = false;
        // this.$emit('successImage', res.data)
        emits('chooseFile', res.url, res.cover)
        showOverlay.value = false
        // videoUrl.value = res.url
        file.status = 'done'
        videoFrame.value = res.cover
      } else {
        showToast(t('toast.uploadImageFail'))
        showOverlay.value = false
        fileList.value = []
        file.status = 'failed'
      }
    })
    .catch((err) => {
      // 接口调用失败之后的操作
      console.log(err)
      showToast(t('toast.uploadImageFail'))
      showOverlay.value = false
      fileList.value = []
      file.status = 'failed'
    })
}
onUnmounted(() => {
  emitter.off('isUploadVideo')
})
</script>

<style scoped lang="scss">
:deep(.van-uploader__file) {
  width: 400px;
  height: 240px;
}

:deep(.van-uploader__preview-delete--shadow) {
  width: 38px;
  height: 38px;
  border-radius: 50%;
}

:deep(.van-uploader__preview-delete-icon) {
  top: 1px;
  right: 3px;
  font-size: 38px;
  color: #fff;
}

:deep(.van-uploader__file-name) {
  display: none;
}

.upload {
  .img {
    width: 160px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #3d556c;
    position: relative;

    img {
      width: 50px;
      height: 50px;
    }

    .message {
      position: absolute;
      top: 0;
      left: 0;
      width: 160px;
      height: 160px;
      z-index: 111;
    }
  }
}
</style>
