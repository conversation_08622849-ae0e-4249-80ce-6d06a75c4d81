<template>
  <div class="vote" :class="[className]">
    <div class="num">{{ $t('match.voteNum', { num: data.voteCount }) }}</div>
    <div class="vote-btn afterVote" v-if="data.isVote == '1'">
      {{ $t('match.voted') }}
    </div>
    <div class="vote-btn disabled" v-else-if="status == '3'">
      {{ $t('match.vote') }}
    </div>
    <div class="vote-btn" v-else @click.stop="onVote">
      {{ $t('match.vote') }}
    </div>
  </div>
  <voteConfirm
    v-if="dialogVote"
    :dialogVote="dialogVote"
    :voteName="data.nickName"
    @dialogClose="dialogClose"
    @confirm="confirm"
  >
  </voteConfirm>
</template>
<script setup>
import { ref, defineProps } from 'vue'
import { voteWorks } from '@/api/match'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import emitter from '@/utils/mitt'
import voteConfirm from '@/components/dialog/vote.vue'
// 接收父组件传递的props
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  className: {
    type: String,
    required: false,
    default: '',
  },
  status: {
    type: [String, Number],
    required: true,
  },
})
const dialogVote = ref(false)
const dialogClose = () => {
  dialogVote.value = false
}
const confirm = async () => {
  try {
    const res = await voteWorks({
      contestId: props.data.contestId,
      id: props.data.id,
      userId: props.data.userIdStr,
    })
    if (res.code == 200) {
      props.data.isVote = '1'
      props.data.voteCount++
      showToast(t('match.voteSuccess'))
      // 判断是否是其他作品 如果是其他刷新列表
      if (props.type == 'other') {
        emitter.emit('refurbishWorkOtherList')
      }
    } else {
      showToast(t('match.voteFail'))
    }
  } catch (error) {
    showToast(t('match.voteFail'))
    console.error('投票失败:', error)
  }
}
const onVote = () => {
  console.log('投票')
  dialogVote.value = true
}
</script>
<style lang="scss" scoped>
.vote {
  display: flex;
  align-items: center;

  .num {
    font-size: 24px;
    color: #666666;
    padding-right: 18px;
  }

  .vote-btn {
    padding: 0 20px;
    height: 44px;
    background: rgba(77, 166, 255, 0.18);
    border-radius: 4px;
    color: #2c85de;
    font-size: 26px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .afterVote {
    background: rgba(141, 141, 141, 0.18);
    color: #808080;
  }

  .disabled {
    cursor: not-allowed;
    background: rgba(141, 141, 141, 0.18);
    color: #808080;
  }
}

.detailVote {
  justify-content: flex-end;
  padding: 8px 24px;
  border-top: 1px solid #e5e5e5;

  .num {
    color: rgba(51, 51, 51, 0.72);
  }
}

.videoVote {
  justify-content: flex-end;
  width: 400px;

  .vote-btn {
    background: #2c85de;
    color: #fff;
  }

  .afterVote {
    background: rgba(141, 141, 141, 0.18);
    color: #808080;
  }

  .disabled {
    cursor: not-allowed;
    background: rgba(141, 141, 141, 0.18);
    color: #808080;
  }
}
</style>
