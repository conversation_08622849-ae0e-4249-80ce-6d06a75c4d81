<template>
  <div class="mask-container" v-if="visible" @click="handleClick">
    <div class="mask-content" :class="[type !== 'receive' ? 'cardsBg' : '']">
      <div class="title" v-if="type === 'obtain'">
        <div class="title-text">
          {{ $t('game.maskObtaining') }}
        </div>
        <div class="title-info">
          {{ $t('game.maskInfo') }}
        </div>
      </div>
      <div class="title" v-if="type === 'synthesis'">
        <div class="title-text">
          {{ $t('game.mergeSuccess') }}
        </div>
        <div class="userInfo">
          <div class="text">{{ $t('game.newFriends') }}：</div>
          <div class="avatar">
            <img :src="data.avatar" alt="头像" />
          </div>
          <div class="name">{{ data.nickName }}</div>
        </div>
      </div>
      <div class="title" v-if="type === 'receive'">
        <div class="title-text">
          {{ $t('game.receiveTitle') }}
        </div>
        <div class="title-info">
          {{ $t('game.receiveInfo') }}
        </div>
      </div>
      <div class="card-container" v-if="type === 'receive'">
        <GameCard
          :color="data.cardColor"
          :number="data.cardNumber"
          cardType="receiveToast"
        />
        <img class="arrow" src="@/assets/images/game/arrow.webp" alt="" />
        <div class="transferCard">
          <div class="button">{{ $t('game.receiveImgText') }}</div>
        </div>
      </div>
      <div class="card-container" v-else>
        <GameCard
          :color="data.cardColor"
          :number="data.cardNumber"
          cardType="toast"
        />
      </div>
      <div v-if="type === 'receive'" class="publish" @click.stop="publish">
        {{ $t('game.receivePublish') }}
      </div>
      <div v-if="type !== 'receive'" class="hint-text">
        {{ $t('game.clickAnywhere') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import GameCard from '@/components/common/GameCard.vue'
import { receiveCard } from '@/api/game.js'
import { showToast } from 'vant'
// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: 'obtain',
  },
  data: {
    type: Object,
    default: () => {},
  },
})

// 定义要发出的事件
const emit = defineEmits(['click', 'receiveCardSuccess'])

// 点击遮罩时触发
const handleClick = () => {
  emit('click')
}
const publish = () => {
  console.log('去邮箱领取奖励')
  const query = {
    id: props.data.id,
  }
  console.log(query)
  receiveCard(query)
    .then((res) => {
      console.log(res)
      if (res.code == 200) {
        // 领取成功
        emit('click')
        emit('receiveCardSuccess')
        showToast(t('game.claimSuccessful'))
      } else {
        emit('click')
        showToast(t('game.claimFailed'))
      }
    })
    .catch(function (error) {
      console.log(error)
      emit('click')
      showToast(t('game.claimFailed'))
    })
}
onMounted(() => {
  // 组件挂载完成
})
</script>

<style scoped lang="scss">
.mask-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.mask-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 730px;
  height: 729px;

  .title {
    margin-top: 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title-text {
      font-family: YouSheBiaoTiHei;
      color: #fffcbb;
      font-size: 53px;
    }

    .title-info {
      color: #fffee3;
      font-size: 28px;
      margin-bottom: 32px;
    }

    .userInfo {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26px;
      margin-bottom: 18px;

      .text {
        color: #fffee3;
      }

      .avatar {
        width: 64px;
        height: 64px;
        margin: 0 14px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .name {
        color: #7cfbff;
      }
    }
  }
}

.cardsBg {
  background: url('@/assets/images/game/maskBg.webp') no-repeat center center;
  background-size: 100% 100%;
}

.card-container {
  margin-bottom: 30px;
  display: flex;
  align-items: center;

  .arrow {
    width: 136px;
    height: 136px;
    margin: 0 32px 0 56px;
  }

  .transferCard {
    width: 316px;
    height: 410px;
    background: url('@/assets/images/game/transferCard.webp') no-repeat;
    background-size: 100% 100%;
    position: relative;

    .button {
      width: 189px;
      height: 30px;
      position: absolute;
      bottom: 44px;
      left: 50%;
      transform: translate(-50%);
      background: url('@/assets/images/game/figure _bar.webp') no-repeat;
      background-size: 100% 100%;
      line-height: 30px;
      text-align: center;
      font-size: 18px;
      color: #333333;
      margin: 0 auto;
      cursor: pointer;
    }
  }
}

.publish {
  // width: 152px;
  height: 62px;
  display: flex;
  align-items: center;
  text-align: center;
  color: #c4e5ff;
  font-size: var(--size_28);
  font-weight: var(--weight5);
  background: url('@/assets/images/index/publish.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 30px;
}

.hint-text {
  color: #ffffff;
  font-size: 28px;
  margin-top: 20px;
  animation: fadeInOut 2s infinite;
}
</style>
