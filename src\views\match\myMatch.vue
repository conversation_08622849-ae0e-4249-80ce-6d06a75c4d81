<template>
  <div class="myMatch">
    <!-- 顶部导航栏 -->
    <div class="nav-tabs">
      <div class="leftNav">
        <!-- 增加一个外层div来处理点击事件 -->
        <div class="back-btn" @click="onClickLeft">
          <img src="@/assets/images/match/back.webp" alt="" />
        </div>
        <span>{{ $t('match.myMatch') }}</span>
      </div>
      <!-- 二级导航 - 使用SubTab组件 -->
      <div class="centerNav">
        <SubTab
          :tabs="subTabs"
          v-model:activeTab="activeTab"
          @tab-click="onClicksubTab"
        />
      </div>
    </div>

    <!-- 内容区域 - 使用MatchList组件 -->
    <MatchList
      ref="matchListRef"
      type="my"
      :active-tab="activeTab"
      :main-tab="mainTabId"
      @scroll-position-saved="onScrollPositionSaved"
      page-type="myMatch"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated, onUnmounted, watch } from 'vue'
import SubTab from '@/components/match/subTab.vue'
import MatchList from '@/components/match/matchList.vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const router = useRouter()
const route = useRoute()
// 定义二级导航标签数据
const subTabs = ref([
  { id: 2, title: t('match.statusProgress') },
  { id: 3, title: t('match.statusEnd') },
])
// 默认选中"进行中"标签
const activeTab = ref(0)

// 主标签ID - 我的比赛使用固定标签（可以根据实际需求调整）
const mainTabId = ref(0) // 0 表示"我的比赛"，这个值需要根据实际API约定确定

// matchList组件的引用
const matchListRef = ref(null)

// 存储滚动位置
const savedScrollPosition = ref(0)

// 接收子组件保存的滚动位置
const onScrollPositionSaved = (position) => {
  savedScrollPosition.value = position
  console.log('父组件接收到滚动位置:', position)
}

// 切换二级tab处理函数
const onClicksubTab = (index) => {
  console.log('我的比赛页面 - 二级标签点击:', index)
  // 标签切换后通知子组件重新布局
  if (matchListRef.value) {
    matchListRef.value.relayoutMasonry()
  }
}

// 返回按钮点击处理函数 - 增强版
const onClickLeft = () => {
  console.log('返回按钮点击')
  try {
    router.back()
    console.log('路由返回操作已执行')
  } catch (error) {
    console.error('路由返回操作失败:', error)
    // 备用方案 - 尝试使用 history API
    try {
      window.history.back()
      console.log('使用 window.history.back() 返回')
    } catch (e) {
      console.error('无法使用任何方式返回:', e)
    }
  }
}

// 监听从详情页返回的事件
const handleFromDetail = () => {
  console.log('从详情页返回')

  // 使用子组件的方法恢复滚动位置
  if (matchListRef.value) {
    matchListRef.value.restoreScrollPosition(savedScrollPosition.value)
  }
}

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (newPath === '/myMatch' && oldPath && oldPath.includes('/matchDetail')) {
      handleFromDetail()
    }
  },
  { immediate: true },
)

// 组件挂载后初始化
onMounted(() => {
  console.log('我的比赛页面加载完成')
})

onActivated(() => {
  console.log('我的比赛页面激活')
  // 恢复滚动位置
  if (matchListRef.value) {
    matchListRef.value.restoreScrollPosition(savedScrollPosition.value)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  // 如有需要，可在这里添加清理代码
})
</script>

<style lang="scss" scoped>
.myMatch {
  background: linear-gradient(180deg, #eeeff2 0%, #e7f1ff 100%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整体溢出
}

/* // 导航标签样式 */
.nav-tabs {
  position: relative; // 设置为相对定位，作为定位参考
  width: 100%;
  padding-bottom: 22px;
  margin-top: 30px;
  flex-shrink: 0; // 防止导航栏被压缩
  z-index: 10;
  height: 40px; // 设置一个固定高度

  .leftNav {
    position: absolute; // 绝对定位
    left: 30px;
    top: 0;
    display: flex;
    align-items: center;
    height: 40px; // 与 SubTab 高度匹配
    z-index: 20; // 确保在其他元素之上

    .back-btn {
      cursor: pointer; // 添加指针样式
      padding: 5px; // 扩大点击区域
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 40px;
        height: 40px;
        display: block;
      }
    }

    span {
      margin-left: 8px;
      font-size: 26px;
      color: rgba(51, 51, 51, 0.8);
    }
  }

  .centerNav {
    position: absolute; // 绝对定位
    left: 0;
    right: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center; // 垂直居中
    width: 100%;
    height: 40px; // 与 SubTab 高度匹配
    pointer-events: none; // 禁用该元素的鼠标事件，使其不捕获点击

    > * {
      pointer-events: auto; // 恢复子元素的鼠标事件，确保 SubTab 可点击
    }
  }
}
</style>
