// const Env = import.meta.env.VITE_APP_TITLE;
// console.log(Env)
// let configs = {}
// if(Env=='production'){
// 	configs = {
//         dynamic:"https://dynamac.hapmetasocialltd.com",
// 		translation:"https://aiap.hapmetasocialltd.com",
// 		audioToText:"https://aiap.hapmetasocialltd.com",
// 		kkfUrl:'https://kkfv.hapmetasocialltd.com/onlinePreview',
// 		fileUrl:'https://fracdn.hapmetasocialltd.com',
// 		thinkingAppId:'22a28ef4590c49c8b955e6b383569665',
// 		apply:'https://loginmeta.hapmetasocialltd.com:9010'
//     }
// }else if(Env=='test'){
//     configs = {
// 		dynamic:"https://dynamactest.hapmetasocialltd.com",
// 		translation:"https://aiaptest.hapmetasocialltd.com",
// 		audioToText:"https://aiaptest.hapmetasocialltd.com",
// 		kkfUrl:'https://kkfvtest.hapmetasocialltd.com/onlinePreview',
// 		fileUrl:'https://fracdn.hapmetasocialltd.com',
// 		thinkingAppId:'17273ca11fde4c948a0bcdc6886fb358',
// 		apply:'https://9010.supergenius.cn'
//     }
// }else{
// 	configs = {
// 		dynamic:"http://192.168.1.106:8808",
// 		translation:"http://18.221.64.66",
// 		audioToText:"http://192.168.1.138:5000",
// 		kkfUrl:'https://kkfvtest.hapmetasocialltd.com/onlinePreview',
// 		fileUrl:'https://fracdn.hapmetasocialltd.com',
// 		thinkingAppId:'17273ca11fde4c948a0bcdc6886fb358',
// 		apply:'http://192.168.1.109:9010'
//     }
// }
// export default configs
const Env = import.meta.env.VITE_APP_TITLE
console.log(Env)
let configs = {}
if (Env == 'production') {
  configs = {
    dynamic: 'https://dynamac.hapmetasocialltd.com',
    translation: 'https://aiap.hapmetasocialltd.com',
    audioToText: 'https://aiap.hapmetasocialltd.com',
    kkfUrl: 'https://kkfv.hapmetasocialltd.com/onlinePreview',
    fileUrl: 'https://fracdn.hapmetasocialltd.com',
    thinkingAppId: '22a28ef4590c49c8b955e6b383569665',
    apply: 'https://loginmeta.hapmetasocialltd.com:9010',
  }
} else if (Env == 'pre') {
  configs = {
    dynamic: 'https://dynamacpre.hapmetasocialltd.com',
    translation: 'https://aiappre.hapmetasocialltd.com',
    audioToText: 'https://aiappre.hapmetasocialltd.com',
    kkfUrl: 'https://kkfvpre.hapmetasocialltd.com/onlinePreview',
    fileUrl: 'https://fracdn.hapmetasocialltd.com',
    thinkingAppId: '22a28ef4590c49c8b955e6b383569665',
    apply: 'https://loginmetapre.hapmetasocialltd.com:7010',
  }
} else if (Env == 'test') {
  configs = {
    dynamic: 'https://dynamactest.hapmetasocialltd.com',
    translation: 'https://aiaptest.hapmetasocialltd.com',
    audioToText: 'https://aiaptest.hapmetasocialltd.com',
    kkfUrl: 'https://kkfvtest.hapmetasocialltd.com/onlinePreview',
    fileUrl: 'https://fracdn.hapmetasocialltd.com',
    thinkingAppId: '17273ca11fde4c948a0bcdc6886fb358',
    apply: 'https://loginmetatest.hapmetasocialltd.com:7010',
  }
} else {
  configs = {
    dynamic: 'http://192.168.1.106:8808',
    translation: 'https://aiaptest.hapmetasocialltd.com',
    audioToText: 'https://aiaptest.hapmetasocialltd.com',
    kkfUrl: 'https://kkfvtest.hapmetasocialltd.com/onlinePreview',
    fileUrl: 'https://fracdn.hapmetasocialltd.com',
    thinkingAppId: '17273ca11fde4c948a0bcdc6886fb358',
    apply: 'https://loginmetatest.hapmetasocialltd.com:7010',
  }
  // configs = {
  // 	dynamic:"http://192.168.1.106:8808",
  // 	translation:"http://18.221.64.66",
  // 	audioToText:"http://192.168.1.138:5000",
  // 	kkfUrl:'https://kkfvtest.hapmetasocialltd.com/onlinePreview',
  // 	fileUrl:'https://fracdn.hapmetasocialltd.com',
  // 	thinkingAppId:'17273ca11fde4c948a0bcdc6886fb358',
  // 	apply:'http://192.168.1.109:9010'
  // }
}
export default configs
