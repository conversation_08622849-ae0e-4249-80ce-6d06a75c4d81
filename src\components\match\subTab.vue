<template>
  <!-- 二级导航 -->
  <div class="match-tabs">
    <van-tabs
      v-model:active="activeTabValue"
      type="card"
      shrink
      @click-tab="onClicksubTab"
    >
      <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title">
        <template #title>
          <span>{{ tab.title }}</span>
        </template>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// 接收父组件传递的props
const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
  activeTab: {
    type: Number,
    default: 0,
  },
})

// 定义要向父组件发出的事件
const emit = defineEmits(['update:activeTab', 'tab-click'])

// 使用计算属性处理 v-model
const activeTabValue = computed({
  get: () => props.activeTab,
  set: (val) => emit('update:activeTab', val),
})

// 处理标签点击事件
const onClicksubTab = (name, tab) => {
  try {
    // 安全访问 tab.index，如果 tab 为 undefined 或 null，则使用 0 作为默认值
    const tabIndex = tab && tab.index !== undefined ? tab.index : 0
    console.log('二级导航标签点击:', tabIndex)
    emit('tab-click', tabIndex)
  } catch (error) {
    console.error('处理标签点击事件出错:', error)
    // 发送默认索引值 0 作为后备方案
    emit('tab-click', 0)
  }
}
</script>

<style lang="scss" scoped>
// 二级导航
.match-tabs {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 750px; // 增加最大宽度，使其更醒目
  margin: 20px auto;
  :deep(.van-tab) {
    font-size: 24px;
    color: rgba(51, 51, 51, 0.7);
    padding: 0 60px; // 保持间距适中
  }

  :deep(.van-tabs__wrap) {
    height: 40px;
  }

  :deep(.van-tabs__nav--card) {
    height: 40px;
    border-color: #b0b0b0;
    margin: 0;
  }

  :deep(.van-tab--card.van-tab--active) {
    color: #fff;
  }

  :deep(.van-tab--card) {
    border-right-color: #b0b0b0;
  }

  :deep(.van-tabs) {
    width: auto; // 自动宽度
    min-width: 600px; // 设置最小宽度
  }
}
</style>
