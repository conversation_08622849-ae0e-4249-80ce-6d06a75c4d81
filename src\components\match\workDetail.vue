<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      round
      @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay"
      :close-on-click-overlay="false"
      teleport="body"
      :overlay-style="{ zIndex: 2000 }"
      :z-index="2001"
    >
      <div class="container">
        <div class="leftCon">
          <div class="top">
            <div class="material">
              <avatar :url="data.avatar" :userId="data.userIdStr"></avatar>
              <div class="info">
                <div class="name">{{ data.nickName }}</div>
                <div class="time">{{ time(data.createTime) }}</div>
              </div>
            </div>
            <div class="rightBtn">
              <translation
                v-if="data.voteContent != null && data.voteContent.length > 0"
                :content="data.voteContent"
                :language="data.language"
                @changeTranslationCon="changeTranslationCon"
              >
              </translation>
              <div class="more">
                <workPopover
                  :data="data"
                  :complainType="data.voteType"
                ></workPopover>
              </div>
              <div class="close" @click="close(index)">
                <img src="@/assets/images/detail/close.png" />
              </div>
            </div>
          </div>
          <div class="centerCon">
            <div class="title">
              <img
                v-if="data.voteTop == 1"
                src="@/assets/images/match/first.webp"
                alt=""
              />
              <img
                v-if="data.voteTop == 2"
                src="@/assets/images/match/second.webp"
                alt=""
              />
              <img
                v-if="data.voteTop == 3"
                src="@/assets/images/match/thirdS.webp"
                alt=""
              />
              <span class="msg">
                {{ translationCon }}
              </span>
            </div>

            <!-- 轮播图 -->
            <Swiper
              v-if="data.voteImageList.length"
              :data="data.voteImageList"
            />
          </div>
          <Vote
            :data="data"
            :type="type"
            :status="status"
            className="detailVote"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { ymdDianTime } from '@/utils/time.js'
import translation from '@/components/common/translation.vue'
const Swiper = defineAsyncComponent(
  () => import('@/components/index/swiper.vue'),
)
const avatar = defineAsyncComponent(
  () => import('@/components/common/avatar.vue'),
)
const Vote = defineAsyncComponent(() => import('@/components/match/vote.vue'))
import workPopover from '@/components/match/workPopover.vue'
import emitter from '@/utils/mitt.js'
import { showToast } from 'vant'
import _ from 'lodash'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    },
  },
  type: {
    type: String,
    default: '',
  },
  isShowDetail: {
    type: Boolean,
    require: false,
    default: false,
  },
  status: {
    type: [String, Number],
    required: true,
  },
})
const translationCon = ref('')
const dialogVisible = ref(false)
onMounted(() => {})
// 计算时间
const time = (data) => {
  return ymdDianTime(data)
}

//获取到翻译组件传得值
const changeTranslationCon = (data) => {
  translationCon.value = data
}

// 定义组件的事件
const emits = defineEmits(['closePopup'])
// 像祖组件传递关闭事件
const close = () => {
  dialogVisible.value = false
  emits('closePopup')
}
// 点击弹出层触发
const onClickPopup = () => {
  console.log('点击事件')
}
//点击遮罩层触发
const onClickOverlay = () => {}
// 关闭弹框
const closeDetail = () => {
  dialogVisible.value = false
  emits('closePopup')
}
watch(
  () => props.isShowDetail,
  (val) => {
    // console.log(val, props.data.id)
    dialogVisible.value = val
    if (dialogVisible.value) {
      translationCon.value = props.data.voteContent
    }
  },
  { immediate: true },
)

// 监听到关闭弹窗
emitter.on('closeDetail', (data) => {
  console.log('关闭关闭关闭')
  dialogVisible.value = false
  emits('closePopup')
})
onUnmounted(() => {
  emitter.off('closeDetail')
})
</script>

<style scoped lang="scss">
:deep(.van-overlay) {
  background: rgba(0, 0, 0, 0.4);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
}

:deep(.van-popup) {
  width: 50%;
  position: fixed !important;
  z-index: 2001;
}

.van-popup--center {
  width: 50%;
  max-width: 60vw;
}

.container {
  overflow: hidden;
  height: 85vh;
  max-height: 90vh;
  width: 100%;
  box-sizing: border-box;

  .leftCon {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .top {
      padding-top: 24px;
      display: flex;
      padding-left: 24px;
      padding-right: 14px;

      .material {
        flex: 1;
        display: flex;

        .avatar {
          width: 80px;
          height: 80px;
        }

        .info {
          margin-left: 10px;

          .name {
            font-size: var(--size_26);
            color: var(--mainTtileColor);
            padding-bottom: 2px;
            font-family: var(--weight5);
            min-height: 37px;
          }

          .time {
            font-size: var(--size_24);
            color: var(--bearTextColor);
          }
        }
      }

      .rightBtn {
        display: flex;
        align-items: center;
        height: 32px;
        font-size: 26px;

        .translation {
          width: 144px;
          height: 32px;
          text-align: center;
          color: #bbe7ff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--size_20);
          text-shadow: 0px 1px 1px rgba(25, 66, 96, 0.87);
          background: url('@/assets/images/detail/fanyiBg.png') no-repeat;
          background-size: 100% 100%;
          // margin-right: 22px;
        }

        .more {
          width: 28px;
          height: 28px;
          margin-left: 22px;
          font-size: 26px;
        }

        .close {
          width: 46px;
          height: 46px;

          img {
            width: 46px;
            height: 46px;
          }
        }
      }
    }

    .centerCon {
      flex: 1;
      overflow: auto;
      margin: 0 24px;

      .title {
        margin: 16px 0 30px 0;

        img {
          width: 46px;
          height: 46px;
          vertical-align: middle;
          margin-right: 10px;
        }

        .msg {
          font-size: var(--size_22);
          color: var(--mainTtileColor);
          line-height: 32px;
          word-wrap: break-word;
          white-space: pre-wrap;
          vertical-align: middle;
          /* 保持换行 */
        }
      }
    }
  }
}
</style>
