const zh_cn = {
  index: {
    title: '动态主页',
    menuItems: {
      Dynamic: '动态',
      Favorites: '收藏',
      Liked: '赞过',
      Messages: '消息',
      weChat: '朋友圈',
      myDynamic: '我的动态',
      myFavorites: '我的收藏',
      myLiked: '我赞过的',
      Hot: '热门',
      Recommend: '推荐',
      New: '最新',
      Vote: '猜猜猜',
      Game: '2048',
    },
    user: {
      praised: '获赞',
      collect: '收藏',
      chat: '好友对话',
      addFriends: '添加好友',
      friendRequest: '好友申请',
      verificationMessage: '验证信息',
      sendNow: '立即发送',
      verificationMessagePlaceholder: '你好呀！很高兴认识你',
    },
    msg: {
      likeCollectTab: '赞和收藏',
      commentAitTab: "评论和{'@'}",
      reply: '回复',
      likeCond: '点赞了你的动态',
      likeConc: '点赞了你的评论',
      likeConr: '点赞了你的回复',
      collectCon: '收藏了你的动态',
      commentCon: '评论了你的动态',
      replyCon: '回复了我',
      aitCon: "{'@'}了你",
    },
  },
  detail: {
    replyNum: '共%num%条回复',
    seeMore: '查看更多',
    stop: '收起',
    originalText: '原文',
    translation: '翻译',
    commentNum: '条评论',
    send: '发送',
    reply: '回复',
    replied: '回复了',
    //   have:'已有',
    likeListTitle: '来自%num%位好友的赞',
  },
  add: {
    title: '发布动态',
    input: '请输入动态内容',
    topic: '添加话题',
    whoSee: {
      title: '可以给谁看',
      all: '所有人',
      friend: '好友可见',
      oneself: '仅自己可见',
    },
    publish: '发布',
    audioStart: '点击说话',
    audioEnd: '点击结束',
    searchPlaceholder: '搜索你的朋友',
    confirm: '确认',
    audioPermission: '请先打开麦克风权限',
    imagePermission: '请先打开相机权限',
    aitUser: "{'@'}用户",
  },
  report: {
    next: '下一步',
    confirmReport: '确认举报',
    placeholder: '请输入内容',
    detailTitle: '详情描述',
    imgTitle: '图片证据',
    reportSuccess: '举报成功',
    reportFail: '举报失败',
    reportSuccessInfo: '提交后平台将积极核实并处理，感谢您对社交环境的维护！',
    reportPublish: '返回',
    reportOneTitleAfterD: '您正在举报%name%的动态',
    reportOneTitleAfterC: '您正在举报%name%的评论',
    reportOneTitleAfterR: '您正在举报%name%的回复',
    reportTwoTitle: '您选择了 %title%',
    reportEndTitleAfterD: '您举报%name%的动态选择了%title%',
    reportEndTitleAfterC: '您举报%name%的评论选择了%title%',
    reportEndTitleAfterR: '您举报%name%的回复选择了%title%',
  },
  tooltip: {
    delete: '删除',
    modify: '修改',
    cancelCollect: '取消收藏',
    report: '举报',
    block: '屏蔽用户',
  },
  delete: {
    deleteCon: '确认要删除该内容吗？',
    deleteCancel: '取消',
    deleteConfirm: '确定',
    blockCon: '确定要屏蔽吗？',
  },
  toast: {
    likeSuccess: '点赞成功',
    likeCancel: '已取消点赞',
    likeFail: '点赞失败',
    collectSuccess: '收藏成功',
    collectCancel: '已取消收藏',
    collectFail: '收藏失败',
    publishSuccess: '发布成功',
    publishFail: '发布失败',
    modifySuccess: '修改成功',
    topicInfo: '最多可选择五个话题哟',
    aitInfo: "最多可{'@'}5名用户",
    ait: "至少输入一个字符再{'@'}哟",
    dynamicInput: '动态内容、图片和视频至少上传一种',
    nextInfo: '请先选择一项',
    reportSuccess: '举报成功',
    reportFail: '举报失败',
    audioTextSuccess: '语音转文字成功',
    audioTextFail: '语音转文字失败',
    translationSuccess: '翻译成功',
    translationFail: '翻译失败',
    uploadImageFail: '上传失败',
    deleteSuccess: '删除成功',
    deleteFail: '删除失败',
    imageLimit: '文件大小不能超过 %num%MB',
    imageNum: '最多可上传9张图',
    uploadPrompt: '请点击上传图片&视频',
    videoLimit: '视频最大不超过25M',
    videoPrompt: '最多上传1个视频',
    videoToast: '只能上传图片或视频',
    imageTitle: '上传图片',
    videoTitle: '上传视频',
    applySuccess: '发送申请成功',
    applyFail: '发送申请失败',
    blacklistPrompt: '黑名单中不能添加好友',
    friendNumPrompt: '对方好友数量已满',
    myNumPrompt: '当前好友数量已满',
    failedPrompt: '参数错误',
    alignPrompt: '已添加对方为好友，不可再次申请',
    applyMyPrompt: '不能添加自己',
    filePrompt: '(支持doc、docx、pdf文件格式，文件大小不能超过5MB)',
    imageBefore: '单张图片不超过4MB',
    imageShowToast: '上传文件过大',
    audioFail: '结束录音出错',
    collectCancelFail: '取消失败',
    collectCancelSuccess: '取消成功',
    dynamicFail: '动态不存在',
    addCommentViolation: '您所提交的内容涉嫌违规，请修改后重新提交。',
    addCommentFail: '添加评论失败',
    addReplyFail: '添加回复失败',
    addDynamicViolation: '您所提交的“动态”内容涉嫌违规，请修改后重新提交。',
    addTopicViolation: '您所提交的“话题”内容涉嫌违规，请修改后重新提交。',
    addImageViolation: '您所提交的“图片”内容涉嫌违规，请修改后重新提交。',
    topicCon: '话题内容不能为空',
    getMsgFail: '获取信息失败',
    loginFail: '登录失败',
    aitInfoPermission: '当前仅自己可见',
    alreadyReport: '您已举报多次，请等待平台反馈',
    commentAfterDelete: '评论已删除',
    replyAfterDelete: '回复已删除',
    msgDataListFail: '数据获取失败',
    alignApply: '已申请好友，可四十八小时后再次申请',
    blockSuccess: '已将用户添加至黑名单',
    blockFail: '屏蔽失败',
    blockListFull: '屏蔽列表已满',
    checkAgreementPrompt: '您未同意《内容发布声明》，无法发布动态',
    AgreementFile: '您已阅读并同意该文件',
    fileTitle: '《内容发布声明》',
    sameLanguagePrompt: '当前处于同一语言，暂不翻译',
  },
  vote: {
    voteProgress: '进行中',
    voteEnd: '已结束',
    voteSettle: '已结算',
    oneselfNum: '已投',
    voteNum: '{num} 币',
    timeName: '剩余时间',
    allNum: '总币数',
    participateInVoting: '总参与人数：',
    getCoins: '本次您获得{num}币',
    voteBtn: '选择',
    voteTitle: '投给{num}',
    inputInfo: '请选择数量',
    voteConfirm: '确认',
    voteSuccess: '成功',
    voteFail: '失败',
    statusEnd: '活动已结束',
    voteTnfo: '参与活动至少投币数量为1',
    hold: '持有',
    balance: '当前账户余额不足，请及时充值',
    remainingTimeData: '{days}天{hours}时{minutes}分{seconds}秒',
    remainingTimeDaysHours: '{days}天{hours}时',
    questionInfo:
      '用户的所有投币将全部计入本次活动的奖池中，猜对的用户按照所投币的数量来瓜分奖池内的所有时空币。',
  },
  video: {
    videoIndex: '说点什么...',
    videoDetail: '详情',
    videoTitle: '视频',
  },
  match: {
    statusProgress: '进行中',
    statusEnd: '已结束',
    matchCon: '比赛内容',
    matchRule: '比赛规则',
    matchAward: '比赛奖励',
    matchInstructions: '参赛须知',
    matchWorks: '参赛作品',
    matchTitle: '赛事',
    matchTime: '报名时间',
    myMatch: '我的比赛',
    statusWaiting: '待开始',
    vote: '投票',
    voted: '已投票',
    voteNum: '{num}票',
    uploadWorks: '上传作品',
    matchAddTitle: '标题',
    titlePlaceholder: '请输入标题',
    submit: '提交',
    content: '内容',
    voteConfirm: '是否对{name}的作品进行投票？',
    voteSuccess: '投票成功',
    voteFail: '投票失败',
    imageShowToast: '标题、图片不能为空',
    videoShowToast: '标题、视频不能为空',
    addWorkTitle: '您所提交的“标题”内容涉嫌违规，请修改后重新提交。',
    addWorkContent: '您所提交的“内容”内容涉嫌违规，请修改后重新提交。',
    againAddWork: '您已参加当前赛事，不可重复参与！',
    myMatchTitle: '我的作品',
    otherMatchTitle: '其他作品',
  },
  empty: {
    comment: '暂无评论',
    list: '暂无数据',
    content: '暂无内容',
    message: '暂无消息',
  },
  game:{
    myCardTitle:`我的卡牌`,
    merge:`合成`,
    maskObtaining:`恭喜获得`,
    maskInfo:`快去添加好友，合成新的卡牌！`,
    clickAnywhere:`点击任意处继续`,
    mergeSuccess:`合成成功`,
    newFriends:`新好友`,
    receiveTitle:`恭喜`,
    receiveInfo:`领取成功后，2048将会消失！`,
    receiveImgText:`传送卡`,
    receivePublish:`去邮箱领取奖励`,
    shop:`商城`,
    purchaseCards:`购买卡牌`,
    totalAmount:`总金额`,
    purchase:`购买`,
    selectCards:`选择卡牌`,
    purchaseSuccessful:`购买成功`,
    purchaseFailed:`购买失败`,
    synthesisFailed:`合成失败`,
    claimSuccessful:`领取成功`,
    claimFailed:`领取失败`,
    exchangeTips:`你拥有2048卡片！用它来兑换一张传送卡！`,
    rule:`游戏规则`,
    ruleNum1:`1.开始游戏后，您会随机获得一张带有颜色的2的基础卡牌`,
    ruleNum2:`2.与拥有相同数字、不同颜色卡牌的其他用户添加好友，可合成更高级的卡牌`,
    ruleNum3:`3.合成成功后，两个账号原始数字的卡牌升级为翻倍数字的新卡牌`,
    ruleNum4:`4.当卡牌上的数字达到2048后，您可以兑换传送卡一张`,
    ruleNum5:`5.合成失败的原因有以下几种：您选择升级的卡牌颜色和对方同色；卡牌已经被其他人使用；您和对方已是好友关系；您的好友数量已到上限；对方好友数量已到上限等。`
  }
}

export default zh_cn
