<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      title=""
      show-cancel-button
      @close="handleClose"
      @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay"
      :close-on-click-overlay="false"
    >
      <div class="deleteCon">
        <img
          class="closeImg"
          @click="handleCancel"
          src="@/assets/images/index/close.png"
        />
        <div class="info">{{ $t('vote.questionInfo') }}</div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
const dialogVisible = ref(false)
const props = defineProps({
  dialogRule: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogRule,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClose'])
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const handleCancel = () => {
  dialogVisible.value = false
}
const onClickPopup = () => {}
const onClickOverlay = () => {}
</script>
<style lang="scss" scoped>
@import '@/assets/css/common/deleteMsg.scss';
.deleteCon {
  width: 684px;
}
.info {
  margin: 48px;
}
</style>
