<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      title=""
      show-cancel-button
      @close="handleClose"
      @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay"
      :close-on-click-overlay="false"
    >
      <div class="deleteCon">
        <div class="top">
          <div class="title">{{ $t('game.rule') }}</div>
          <img @click="handleCancel" src="@/assets/images/friend/close.webp" />
        </div>
        <div class="bottomCon">
          <div class="ruleInfo">
            <div>{{ $t('game.ruleNum1') }}</div>
            <div>{{ $t('game.ruleNum2') }}</div>
            <div>{{ $t('game.ruleNum3') }}</div>
            <div>{{ $t('game.ruleNum4') }}</div>
            <div>{{ $t('game.ruleNum5') }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
const dialogVisible = ref(false)
const props = defineProps({
  dialogGameRule: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogGameRule,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClose'])
// 取消按钮
const handleCancel = () => {
  emits('dialogClose')
}
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const onClickPopup = () => {}
const onClickOverlay = () => {}
onMounted(() => {})
</script>
<style lang="scss" scoped>
:deep(.van-popup) {
  background: none;
}

:deep(.van-cell) {
  background: #a9c3db;
}

:deep(.van-cell__value) {
  font-size: var(--size_24);
}

:deep(.van-field__word-limit) {
  font-size: var(--size_22);
}

.deleteCon {
  width: 900px;
  background: linear-gradient(180deg, #eaf5ff 0%, #ffffff 100%);
  border-radius: 10px;
  display: flex;
  flex-direction: column;

  .top {
    height: 80px;
    padding: 0 16px 0 24px;
    display: flex;
    align-items: center;
    background: linear-gradient(
      180deg,
      #4289ec 0%,
      rgba(126, 194, 255, 0.63) 100%
    );
    background-size: 100% 100%;

    .title {
      color: #333333;
      font-size: 28px;
      flex: 1;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .bottomCon {
    flex: 1;
    background: rgba(204, 231, 255, 0.4);
    padding: 24px;
    margin: 16px;

    .ruleInfo {
      font-size: 28px;
      color: rgba(51, 51, 51, 0.9);
      line-height: 40px;
    }
  }
}
</style>
