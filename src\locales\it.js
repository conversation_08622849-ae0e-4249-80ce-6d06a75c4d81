const it = {
  index: {
    title: 'Post della home page',
    menuItems: {
      Dynamic: 'Post',
      Favorites: '<PERSON><PERSON><PERSON>',
      Liked: 'Apprezzato',
      Messages: 'Messaggio',
      weChat: 'Momenti',
      myDynamic: 'I miei post',
      myFavorites: 'I miei preferiti',
      myLiked: 'I miei apprezzamenti',
      Hot: 'Tendenze',
      Recommend: 'Raccomandare',
      New: 'Ultimo',
      Vote: 'Indovinare&Guadagnare',
      Game: '2048',
    },
    user: {
      praised: 'Guadagna apprezzamenti',
      collect: 'Ra<PERSON><PERSON>',
      chat: 'Chat',
      addFriends: 'Aggiungi amici',
      friendRequest: 'Richiesta di amicizia',
      verificationMessage: 'Informazione di verifica',
      sendNow: 'Invia immediatamente',
      verificationMessagePlaceholder: 'Come stai! Piacere di conoscerti',
    },
    msg: {
      likeCollectTab: 'Apprezza e aggiungi ai preferiti',
      commentAitTab: "Commenti e {'@'}",
      reply: 'Rispondi',
      likeCond: 'Ho apprezzato il tuo post',
      likeConc: 'Mi è piaciuto il tuo commento',
      likeConr: 'Mi è piaciuta la tua risposta',
      collectCon: 'Ho salvato il tuo post',
      commentCon: 'Ho commentato il tuo post',
      replyCon: 'Risposto',
      aitCon: "{'@'}te",
    },
  },
  detail: {
    replyNum: 'Un totale di %num% risposte',
    seeMore: 'Vedi di più',
    stop: 'Ripiega',
    originalText: 'Testo originale',
    translation: 'Traduci',
    commentNum: 'commenti',
    send: 'Invia',
    reply: 'Rispondi',
    replied: 'Risposto',
    //   have:'已有',
    likeListTitle: 'Mi piace di %num% amici',
  },
  add: {
    title: 'Posta',
    input: 'Inserisci il contenuto del post',
    topic: 'Aggiungi argomento',
    whoSee: {
      title: 'A chi posso mostrarlo',
      all: 'Tutti',
      friend: 'Gli amici possono vedere',
      oneself: 'Visibile solo a te stesso',
    },
    publish: 'Rilascia',
    audioStart: 'Clicca per parlare',
    audioEnd: 'Clicca per terminare',
    searchPlaceholder: 'Cerca i tuoi amici',
    confirm: 'Conferma',
    audioPermission: 'Si prega di aprire prima il permesso del microfono',
    imagePermission: 'Si prega di abilitare prima i permessi della fotocamera',
    aitUser: "{'@'}utente",
  },
  report: {
    next: 'Passo successivo',
    confirmReport: 'Conferma il rapporto',
    placeholder: 'Si prega di inserire contenuto',
    detailTitle: 'Descrizione dettagliata',
    imgTitle: 'Prova fotografica',
    reportSuccess: 'Segnalazione riuscita',
    reportFail: 'Segnalazione non riuscita',
    reportSuccessInfo:
      "Dopo l'invio, la piattaforma verificherà e elaborerà attivamente; grazie per il tuo impegno nel mantenere l'ambiente sociale!",
    reportPublish: 'Indietro',
    reportOneTitleAfterD: 'Stai segnalando il post di %name%',
    reportOneTitleAfterC: 'Stai segnalando un commento di %name%',
    reportOneTitleAfterR: 'Stai segnalando una risposta di %name%',
    reportTwoTitle: 'Hai selezionato %title%',
    reportEndTitleAfterD:
      'Il tuo rapporto per il post di %name% appartiene a %title%',
    reportEndTitleAfterC:
      'Il tuo rapporto per commenti di %name% appartiene a %title%',
    reportEndTitleAfterR:
      'Il tuo rapporto per le risposte di %name% appartiene a %title%',
  },
  tooltip: {
    delete: 'Elimina',
    modify: 'Modifica',
    cancelCollect: 'Rimuovi dai preferiti',
    report: 'Segnala',
    block: 'Blocca',
  },
  delete: {
    deleteCon: 'Sei sicuro di voler eliminare questo contenuto?',
    deleteCancel: 'Annulla',
    deleteConfirm: 'Conferma',
    blockCon: 'Vuoi davvero bloccare?',
  },
  toast: {
    likeSuccess: 'Mi è piaciuto con successo',
    likeCancel: 'Mi piace è stato annullato',
    likeFail: 'Mi piace non riuscito',
    collectSuccess: 'Preferito con successo',
    collectCancel: 'Rimosso dai preferiti',
    collectFail: 'Preferito non riuscito',
    publishSuccess: 'Post pubblicato con successo',
    publishFail: 'Post non riuscito',
    modifySuccess: 'Modifica riuscita',
    topicInfo: 'Puoi scegliere fino a 5 argomenti',
    aitInfo: "Fino a {'@'}5 utenti.",
    ait: "Si prega di inserire almeno 1 carattere prima di {'@'}",
    dynamicInput:
      'Carica almeno uno dei seguenti: contenuto dinamico, immagini o video',
    nextInfo: "Si prega di selezionare prima un'opzione",
    reportSuccess: 'Segnalazione riuscita',
    reportFail: 'Segnalazione non riuscita',
    audioTextSuccess: 'Voce in testo riuscita',
    audioTextFail: 'Voce in testo non riuscita',
    translationSuccess: 'Traduzione riuscita',
    translationFail: 'Traduzione non riuscita',
    uploadImageFail: 'Caricamento non riuscito',
    deleteSuccess: 'Eliminazione riuscita',
    deleteFail: 'Cancellazione non riuscita',
    // 新加
    imageLimit: 'La dimensione del file non può superare i %num%MB',
    imageNum: 'Fino a 9 immagini possono essere caricate',
    uploadPrompt: 'Si prega di fare clic per caricare immagini & video',
    filePrompt:
      '(supportano i formati di file Doc, docx e pdf, e la dimensione del file non può superare 5 mb)',
    imageBefore: "Non più di 4 MB per un'immagine singola",
    imageShowToast: 'Il file caricato è troppo grande',
    audioFail: 'Errore nella fine della registrazione',
    collectCancelFail: 'Cancellazione non riuscita',
    collectCancelSuccess: 'Cancellazione riuscita',
    dynamicFail: 'Il post non esiste',
    addCommentViolation:
      'Il contenuto che hai inviato è sospettato di violare le normative, si prega di modificare e reinviare. Modifica e reinvia.',
    addCommentFail: 'Impossibile aggiungere commento',
    addReplyFail: 'Impossibile aggiungere risposta.',
    addDynamicViolation:
      'Il contenuto del "post" che hai inviato è sospettato di violare le normative, ti preghiamo di modificarlo e ripresentarlo.',
    addTopicViolation:
      'Il contenuto del "topic" che hai inviato è sospettato di violare le normative, ti preghiamo di modificarlo e ripresentarlo.',
    addImageViolation: `Il contenuto dell'"immagine" che hai inviato è sospettato di violare le normative, ti preghiamo di modificarlo e ripresentarlo.`,
    topicCon: 'Il contenuto del topic non può essere vuoto',
    getMsgFail: 'Impossibile recuperare informazioni',
    loginFail: 'Accesso non riuscito',
    aitInfoPermission: 'Attualmente visibile solo a te stesso',
    alreadyReport:
      'Hai segnalato più volte, ti preghiamo di attendere il feedback della piattaforma',
    commentAfterDelete: 'Commenti cancellati',
    replyAfterDelete: 'Risposta cancellata',
    msgDataListFail: 'Acquisizione dati fallita',
    videoLimit: 'Il video non deve superare i 25 MB',
    videoPrompt: 'È possibile caricare un massimo di 1 video',
    videoToast: 'Si possono caricare solo immagini o video.',
    imageTitle: 'Carica immagine',
    videoTitle: 'Carica video',
    applySuccess: 'Richiesta inviata con successo',
    applyFail: 'Invio della richiesta non riuscito',
    blacklistPrompt: 'Non è possibile aggiungere amici dalla lista nera',
    friendNumPrompt: "Il numero di amici dell'altro utente è al completo",
    myNumPrompt: 'Il numero attuale di amici è al completo',
    failedPrompt: 'Errore nei parametri',
    alignPrompt:
      'Hai già aggiunto questa persona come amico, non puoi inviare nuovamente la richiesta',
    applyMyPrompt: 'Non puoi aggiungerti da solo',
    alignApply:
      "Hai inviato una richiesta di amicizia. Puoi inviarne un'altra dopo 48 ore",
    blockSuccess: "L'utente è stato aggiunto alla lista nera",
    blockFail: 'Il blocco è fallito',
    blockListFull: 'La lista dei blocchi è piena',
    checkAgreementPrompt:
      "Non sei d'accordo con 《Dichiarazione di pubblicazione del contenuto》 e non puoi pubblicare le dinamiche",
    AgreementFile: 'Hai letto e accettato il documento',
    fileTitle: '《Dichiarazione di pubblicazione del contenuto》',
    sameLanguagePrompt:
      'Attualmente nella stessa lingua, non è necessaria una traduzione',
  },
  vote: {
    voteProgress: 'In corso',
    voteEnd: 'Terminato',
    voteSettle: 'Regolato',
    oneselfNum: 'Ha votato',
    voteNum: '{num} Moneta',
    timeName: 'Tempo rimanente',
    allNum: 'Numero totale di monete',
    participateInVoting: 'Numero totale di giocatori:',
    getCoins: 'Questa volta, hai guadagnato {num} monete',
    voteBtn: 'Scegliere',
    voteTitle: 'Votare per{num}',
    inputInfo: 'Seleziona la quantità',
    voteConfirm: 'Conferma',
    voteSuccess: 'Successo',
    voteFail: 'Fallimento',
    statusEnd: "L'evento è terminato",
    voteTnfo:
      "Il numero minimo di monete richiesto per partecipare all'evento è di 1",
    hold: 'Detenere',
    balance: 'Il tuo saldo attuale è insufficiente. Ricarica tempestivamente',
    remainingTimeData: '{days} giorni {hours} ore {minutes} min {seconds} sec',
    remainingTimeDaysHours: '{days} giorni {hours} ore',
    questionInfo:
      'Tutte le monete degli utenti andranno nel pool dei premi di questo evento, e gli utenti che indovinano correttamente si divideranno tutte le 【monete tempo-spazio】 nel pool dei premi in base al numero di monete indovinate.',
  },
  video: {
    videoIndex: 'Di qualcosa...',
    videoDetail: 'Pagina dettagli',
    videoTitle: 'Video',
  },
  match: {
    statusProgress: 'In corso',
    statusEnd: 'Terminato',
    matchCon: 'Contenuto della competizione',
    matchRule: 'Regole della competizione',
    matchAward: 'Premio della competizione',
    matchInstructions: 'Linee guida per la concorrenza',
    matchWorks: 'Produzione',
    matchTitle: 'Competizione',
    matchTime: 'Periodo di registrazione',
    myMatch: 'Le mie competizioni',
    statusWaiting: 'In attesa di inizio',
    vote: 'Voto',
    voted: 'Ha votato',
    voteNum: '{num} voti',
    uploadWorks: 'Carica la tua opera',
    matchAddTitle: 'Titolo',
    titlePlaceholder: 'Inserisci un titolo',
    submit: 'Invio',
    content: 'Contenuto',
    voteConfirm: 'Vuoi votare per la produzione di {name}?',
    voteSuccess: 'Voto riuscito',
    voteFail: 'Voto fallito',
    imageShowToast: 'Titolo e immagine non possono essere vuoti',
    videoShowToast: 'Titolo e video non possono essere vuoti',
    addWorkTitle:
      'Il contenuto del "titolo" inviato è sospetto di violazione delle regole. Si prega di modificare e inviare di nuovo.',
    addWorkContent:
      'Il contenuto del "contenuto" inviato è sospetto di violazione delle regole. Si prega di modificare e inviare di nuovo.',
    againAddWork:
      'Hai già partecipato alla competizione attuale e non puoi partecipare di nuovo!',
    myMatchTitle: 'Le mie opere',
    otherMatchTitle: 'Opere di altri',
  },
  empty: {
    comment: 'Nessun commento ancora',
    list: 'Nessun dato disponibile',
    content: 'Nessun contenuto disponibile',
    message: 'Nessun messaggio disponibile',
  },
  game:{
    myCardTitle:`Le mie carte`,
    merge:`Combinare`,
    maskObtaining:`Congratulazioni per aver vinto`,
    maskInfo:`Sbrigati e aggiungi amici per sintetizzare nuove carte!`,
    clickAnywhere:`Fai clic ovunque per continuare`,
    mergeSuccess:`Sintesi riuscita`,
    newFriends:`Nuovo amico`,
    receiveTitle:`Congratulazioni!`,
    receiveInfo:`Dopo il successo della richiesta, il 2048 scomparirà!`,
    receiveImgText:`Carta di teletrasporto`,
    receivePublish:`Vai alla cassetta delle lettere per chiedere il premio`,
    shop:`Centro commerciale`,
    purchaseCards:`Acquista carte`,
    totalAmount:`Importo totale`,
    purchase:`L'acquisto`,
    selectCards:`Seleziona carte`,
    purchaseSuccessful:`Acquisto riuscito`,
    purchaseFailed:`Acquisto non riuscito`,
    synthesisFailed:`Sintesi fallita`,
    claimSuccessful:`Richiesta riuscita`,
    claimFailed:`Credito non riuscito`,
    exchangeTips:`Hai la carta 2048! Lo riscatta per una carta di teleporto!`,
    rule:`Regole del gioco`,
    ruleNum1:`1.Dopo aver iniziato il gioco, riceverai casualmente una carta di base con colori 2`,
    ruleNum2:`2.Aggiungi amici con altri utenti con lo stesso numero e carte di colori diversi per creare carte più avanzate`,
    ruleNum3:`3.Dopo il successo della sintesi, le carte con il numero originale di due conti vengono aggiornate a nuove carte con il doppio del numero`,
    ruleNum4:`4.Quando il numero sulla carta raggiunge 2048, puoi riscattare una carta di trasferimento.`,
    ruleNum5:`5.Ci sono le seguenti ragioni per cui la sintesi non riesce: scegli il colore della carta aggiornata e il colore dell'altra; la carta è già stata utilizzata da altri; Siete già amici l'uno con l'altro; Il numero massimo di amici è stato raggiunto; Il numero di amici ha raggiunto il limite massimo.`
  }
}

export default it
