import axios from 'axios'
import config from '@/utils/env.js'
// http://192.168.1.138:5001/login
// 服务器请求地址
// http://18.221.64.66
const baseURL1 = config.translation //翻译
const baseURL2 = config.audioToText //语音转文字
const baseURL4 = config.apply //语音转文字
const baseURL3 = 'http://192.168.1.138:5001' //登录
// 服务器请求地址
let request = axios.create({
  timeout: 20000,
})
// 拦截器的添加
request.interceptors.request.use(
  (config) => {
    switch (config.urlType) {
      case 'api1':
        config.url = baseURL1 + config.url
        break
      case 'api2':
        config.url = baseURL2 + config.url
        // const apiTitle=process.env.NODE_ENV === 'development' ?'/api':'/api2'
        // config.url = apiTitle+config.url;
        // console.log( config.url)
        break
      case 'api3':
        config.url = baseURL4 + config.url
        break
      default:
        config.url = baseURL3 + config.url
    }
    // config.headers['Authorization'] ="MTAyMzY0Mzc3NTY3NjcxMDkxMjoxNzI5NzYwMjg3"; //携带token

    //注意使用token的时候需要引入cookie方法或者用本地localStorage等方法，推荐js-cookie
    // const token = getCookie('名称');//这里取token之前，你肯定需要先拿到token,存一下
    // if(token){
    //   config.params = {'token':token} //如果要求携带在参数中
    //   config.headers.token= token; //如果要求携带在请求头中
    // }
    return config
  },
  (err) => {
    return Promise.reject(err)
  },
)
//响应拦截器
request.interceptors.response.use(
  (res) => {
    return res.data
  },
  (err) => {
    return Promise.reject(err)
  },
)
export default request
