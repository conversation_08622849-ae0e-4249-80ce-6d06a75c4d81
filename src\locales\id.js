const id = {
  index: {
    title: '<PERSON><PERSON>a pos',
    menuItems: {
      Dynamic: 'Pos',
      Favorites: 'Kumpul<PERSON>',
      Liked: '<PERSON><PERSON><PERSON>',
      Messages: '<PERSON><PERSON>',
      weChat: 'Momen',
      myDynamic: '<PERSON><PERSON> saya',
      myFavorites: '<PERSON>avorit saya',
      myLiked: 'Yang saya sukai',
      Hot: 'Tren',
      Recommend: 'Rekomendasi',
      New: 'Terbaru',
      Vote: 'Tebak&Dapatkan',
      Game: '2048',
    },
    user: {
      praised: 'Dapatkan suka',
      collect: 'Kumpulkan',
      chat: 'Obrolan',
      addFriends: 'Tambahkan teman',
      friendRequest: 'Permin<PERSON><PERSON>',
      verificationMessage: 'Informasi Verifikasi',
      sendNow: '<PERSON><PERSON>',
      verificationMessagePlaceholder: 'Apa kabar! Senang bertemu denganmu.',
    },
    msg: {
      likeCollectTab: 'Suka dan favorit',
      commentAitTab: "Komentar dan {'@'}",
      reply: '<PERSON><PERSON>',
      likeCond: 'Saya menyukai pos Anda',
      likeConc: '<PERSON><PERSON><PERSON> komentar Anda',
      likeConr: '<PERSON><PERSON><PERSON> balasan Anda',
      collectCon: '<PERSON>a telah menyimpan pos Anda',
      commentCon: 'Mengomentari pos Anda',
      replyCon: 'Menjawab',
      aitCon: "{'@'}Anda",
    },
  },
  detail: {
    replyNum: 'Total %num% balasan',
    seeMore: 'Lihat lebih banyak',
    stop: 'Lipatan',
    originalText: 'Teks asli',
    translation: 'Terjemahkan',
    commentNum: 'komentar',
    send: 'Kirim',
    reply: 'Balas',
    replied: 'Menjawab',
    //   have:'已有',
    likeListTitle: 'Like dari %num% teman',
  },
  add: {
    title: 'Pos',
    input: 'Silakan masukkan konten Pos',
    topic: 'Tambahkan topik',
    whoSee: {
      title: 'Siapa yang bisa saya tunjukkan',
      all: 'Semua orang',
      friend: 'Teman dapat melihat',
      oneself: 'Hanya terlihat untuk diri sendiri',
    },
    publish: 'Rilis',
    audioStart: 'Klik untuk berbicara',
    audioEnd: 'Klik untuk mengakhiri',
    searchPlaceholder: 'Cari teman Anda',
    confirm: 'Konfirmasi',
    audioPermission: 'Silakan buka izin mikrofon terlebih dahulu',
    imagePermission: 'Silakan aktifkan izin kamera terlebih dahulu',
    aitUser: "{'@'}pengguna",
  },
  report: {
    next: 'Langkah selanjutnya',
    confirmReport: 'Konfirmasi laporan',
    placeholder: 'Silakan masukkan konten',
    detailTitle: 'Deskripsi rinci',
    imgTitle: 'Bukti gambar',
    reportSuccess: 'Laporan berhasil',
    reportFail: 'Laporan gagal',
    reportSuccessInfo:
      'Setelah pengiriman, platform akan secara aktif memverifikasi dan memproses; terima kasih atas usaha Anda untuk menjaga lingkungan sosial!',
    reportPublish: 'Kembali',
    reportOneTitleAfterD: 'Anda melaporkan postingan %name%',
    reportOneTitleAfterC: 'Anda melaporkan komentar %name%',
    reportOneTitleAfterR: 'Anda melaporkan balasan %name%',
    reportTwoTitle: 'Anda telah memilih %title%',
    reportEndTitleAfterD: 'Laporan Anda untuk postingan %name% milik %title%',
    reportEndTitleAfterC: 'Laporan Anda untuk komentar %name% milik %title%',
    reportEndTitleAfterR: 'Laporan Anda untuk balasan %name% milik %title%',
  },
  tooltip: {
    delete: 'Hapus',
    modify: 'Modifikasi',
    cancelCollect: 'Hapus favorit',
    report: 'Laporkan',
    block: 'Blokir',
  },
  delete: {
    deleteCon: 'Apakah Anda yakin ingin menghapus konten ini?',
    deleteCancel: 'Batalkan',
    deleteConfirm: 'Konfirmasi',
    blockCon: 'Apakah Anda yakin ingin memblokir?',
  },
  toast: {
    likeSuccess: 'Suka berhasil',
    likeCancel: 'Suka telah dibatalkan',
    likeFail: 'Suka gagal',
    collectSuccess: 'Favorit berhasil',
    collectCancel: 'Dihapus dari favorit',
    collectFail: 'Favorit gagal',
    publishSuccess: 'Pos berhasil',
    publishFail: 'Pos gagal',
    modifySuccess: 'Modifikasi berhasil',
    topicInfo: 'Anda dapat memilih hingga 5 topik',
    aitInfo: "Hingga {'@'}5 pengguna",
    ait: "Silakan masukkan setidaknya 1 karakter sebelum {'@'}",
    dynamicInput:
      'Harap unggah setidaknya salah satu dari berikut: konten dinamis, gambar atau video',
    nextInfo: 'Silakan pilih satu opsi terlebih dahulu',
    reportSuccess: 'Laporan berhasil',
    reportFail: 'Laporan gagal',
    audioTextSuccess: 'Sukses suara ke teks',
    audioTextFail: 'Gagal suara ke teks',
    translationSuccess: 'Terjemahan berhasil',
    translationFail: 'Terjemahan gagal',
    uploadImageFail: 'Unggah gagal',
    deleteSuccess: 'Hapus berhasil',
    deleteFail: 'Penghapusan gagal',
    // 新加
    imageLimit: 'Ukuran file tidak boleh melebihi %num%MB',
    imageNum: 'Hingga 9 gambar dapat diunggah',
    uploadPrompt: 'Silakan klik untuk mengunggah gambar & video',
    filePrompt:
      '(format file Doc, docx, dan pdf didukung, dan ukuran file tidak boleh melebihi 5mb)',
    imageBefore: 'Tidak lebih dari 4mb untuk satu gambar',
    imageShowToast: 'Upload file terlalu besar',
    audioFail: 'Kesalahan mengakhiri rekaman',
    collectCancelFail: 'Pembatalan gagal',
    collectCancelSuccess: 'Pembatalan berhasil',
    dynamicFail: 'Pos tidak ada',
    addCommentViolation:
      'Konten yang Anda kirimkan diduga melanggar peraturan, silakan modifikasi dan kirim ulang. Modifikasi dan kirim ulang.',
    addCommentFail: 'Gagal menambahkan komentar',
    addReplyFail: 'Gagal menambahkan balasan',
    addDynamicViolation:
      'Konten "pos" yang Anda kirimkan diduga melanggar peraturan, silakan modifikasi dan kirim ulang.',
    addTopicViolation:
      'Konten "topik" yang Anda kirimkan diduga melanggar peraturan, silakan modifikasi dan kirim ulang.',
    addImageViolation:
      'Konten "gambar" yang Anda kirimkan diduga melanggar peraturan, silakan modifikasi dan kirim ulang.',
    topicCon: 'Konten topik tidak boleh kosong',
    getMsgFail: 'Gagal mengambil informasi',
    loginFail: 'Gagal masuk',
    aitInfoPermission: 'Saat ini hanya terlihat untuk diri sendiri',
    alreadyReport:
      'Anda telah melaporkan beberapa kali, silakan tunggu umpan balik dari platform',
    commentAfterDelete: 'Komentar Dihapus',
    replyAfterDelete: 'Balasan dihapus',
    msgDataListFail: 'Akuisisi data gagal',
    videoLimit: 'Video tidak boleh melebihi 25 MB',
    videoPrompt: 'Anda dapat mengunggah maksimal 1 video',
    videoToast: 'Hanya gambar atau video yang dapat diunggah.',
    imageTitle: 'Unggah gambar',
    videoTitle: 'Unggah video',
    applySuccess: 'Permintaan Terkirim dengan Sukses',
    applyFail: 'Gagal Mengirim Permintaan',
    blacklistPrompt: 'Tidak Bisa Menambah Teman dari Daftar Hitam',
    friendNumPrompt: 'Jumlah Teman Pengguna Lain Sudah Penuh',
    myNumPrompt: 'Jumlah Teman Saat Ini Sudah Penuh',
    failedPrompt: 'Kesalahan Parameter',
    alignPrompt:
      'Anda Sudah Menambah Orang Ini Sebagai Teman, Tidak Bisa Mengirim Permintaan Lagi',
    applyMyPrompt: 'Tidak Bisa Menambah Diri Sendiri',
    alignApply:
      'Anda telah mengirim permintaan teman. Anda dapat mengirim permintaan lain dalam 48 jam',
    blockSuccess: 'Pengguna telah ditambahkan ke daftar hitam',
    blockFail: 'Gagal memblokir',
    blockListFull: 'Daftar blokir penuh',
    checkAgreementPrompt:
      'Anda tidak setuju dengan《Pernyataan Publikasi Konten》dan tidak dapat memposting berita',
    AgreementFile: 'Anda telah membaca dan menyetujui dokumen ini',
    fileTitle: '《Pernyataan Publikasi Konten》',
    sameLanguagePrompt:
      'Saat ini menggunakan bahasa yang sama, tidak perlu diterjemahkan',
  },
  vote: {
    voteProgress: 'Sedang berlangsung',
    voteEnd: 'Telah selesai',
    voteSettle: 'Telah diselesaikan',
    oneselfNum: 'Sudah memilih',
    voteNum: '{num} Koin',
    timeName: 'Waktu tersisa',
    allNum: 'Jumlah total koin',
    participateInVoting: 'Jumlah total pemain:',
    getCoins: 'Kali ini, Anda mendapatkan {num} koin',
    voteBtn: 'Memilih',
    voteTitle: 'Vote untuk{num}',
    inputInfo: 'Silakan pilih jumlah',
    voteConfirm: 'Konfirmasi',
    voteSuccess: 'Sukses',
    voteFail: 'Kegagalan',
    statusEnd: 'Acara telah berakhir',
    voteTnfo:
      'Jumlah koin minimum yang diperlukan untuk berpartisipasi dalam acara adalah 1',
    hold: 'Memegang',
    balance:
      'Saldo akun Anda saat ini tidak mencukupi. Silakan isi ulang tepat waktu',
    remainingTimeData:
      '{days} hari {hours} jam {minutes} menit {seconds} detik',
    remainingTimeDaysHours: '{days} hari {hours} jam',
    questionInfo:
      'Semua koin pengguna akan masuk ke dalam kolam hadiah untuk acara ini, dan pengguna yang menebak dengan benar akan membagi semua 【koin waktu-ruang】 dalam kolam hadiah sesuai dengan jumlah koin yang mereka tebak.',
  },
  video: {
    videoIndex: 'Katakan sesuatu...',
    videoDetail: 'Halaman detail',
    videoTitle: 'Video',
  },
  match: {
    statusProgress: 'Sedang berlangsung',
    statusEnd: 'Selesai',
    matchCon: 'Konten kompetisi',
    matchRule: 'Aturan kompetisi',
    matchAward: 'Hadiah kompetisi',
    matchInstructions: 'Pedoman Persaingan',
    matchWorks: 'Produksi',
    matchTitle: 'Kompetisi',
    matchTime: 'Waktu pendaftaran',
    myMatch: 'Kompetisiku',
    statusWaiting: 'Menunggu mulai',
    vote: 'Voting',
    voted: 'Sudah memilih',
    voteNum: '{num} suara',
    uploadWorks: 'Unggah karya Anda',
    matchAddTitle: 'Judul',
    titlePlaceholder: 'Silakan masukkan judul',
    submit: 'Pengajuan',
    content: 'Konten',
    voteConfirm: 'Inginkah Anda memilih produksi {name}?',
    voteSuccess: 'Voting berhasil',
    voteFail: 'Voting gagal',
    imageShowToast: 'Judul dan gambar tidak boleh kosong',
    videoShowToast: 'Judul dan video tidak boleh kosong',
    addWorkTitle:
      'Konten "judul" yang Anda kirim dicurigai melanggar aturan. Silakan ubah dan kirim ulang.',
    addWorkContent:
      'Konten "konten" yang Anda kirim dicurigai melanggar aturan. Silakan ubah dan kirim ulang.',
    againAddWork:
      'Anda sudah berpartisipasi dalam kompetisi ini dan tidak dapat berpartisipasi lagi!',
    myMatchTitle: 'Karya saya',
    otherMatchTitle: 'Karya orang lain',
  },
  empty: {
    comment: 'Belum ada komentar',
    list: 'Tidak ada data tersedia',
    content: 'Tidak ada konten tersedia',
    message: 'Tidak ada pesan tersedia',
  },
  game:{
    myCardTitle:`Kartuku.`,
    merge:`Menggabungkan`,
    maskObtaining:`Selamat atas kemenanganmu.`,
    maskInfo:`Cepat dan tambahkan teman untuk mensintesis kartu baru!`,
    clickAnywhere:`Klik di mana saja untuk melanjutkan`,
    mergeSuccess:`Sintesis sukses`,
    newFriends:`Teman baru`,
    receiveTitle:`Selamat!`,
    receiveInfo:`Setelah klaim berhasil, 2048 akan menghilang!`,
    receiveImgText:`Kartu teleport`,
    receivePublish:`Pergi ke kotak surat untuk meminta hadiah`,
    shop:`Mal`,
    purchaseCards:`Beli kartu`,
    totalAmount:`Jumlah Total`,
    purchase:`Pembelian`,
    selectCards:`Pilih kartu`,
    purchaseSuccessful:`Beli dengan sukses`,
    purchaseFailed:`Pembelian gagal`,
    synthesisFailed:`Sintesis gagal`,
    claimSuccessful:`Klaim berhasil`,
    claimFailed:`Klaim gagal`,
    exchangeTips:`Anda memegang kartu 2048! Tukarnya untuk Kartu Teleport!`,
    rule:`Aturan permainan`,
    ruleNum1:`1.Setelah memulai permainan, Anda akan mendapatkan kartu dasar 2 dengan warna secara acak`,
    ruleNum2:`2.Tambahkan teman dengan pengguna lain dengan nomor yang sama, kartu warna yang berbeda, untuk membuat kartu yang lebih maju`,
    ruleNum3:`3.Setelah sintesis berhasil, kartu dengan nomor asli dua akun ditingkatkan menjadi kartu baru dengan nomor ganda`,
    ruleNum4:`4.Setelah angka pada kartu mencapai 2048, Anda dapat menukarkan satu kartu transfer.`,
    ruleNum5:`5.Ada beberapa alasan untuk kegagalan sintesis: Anda memilih warna kartu yang ditingkatkan dan warna yang sama satu sama lain; Kartu telah digunakan oleh orang lain; Anda dan orang lain sudah memiliki hubungan yang baik; Jumlah teman Anda telah mencapai batas; Jumlah teman telah mencapai batas terbatas.`
  }
}

export default id
