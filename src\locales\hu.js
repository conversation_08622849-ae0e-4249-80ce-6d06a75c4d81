const zh_cn = {
  index: {
    title: '<PERSON><PERSON>old<PERSON>',
    menuItems: {
      Dynamic: 'Bejegyzések',
      Favorites: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      Liked: '<PERSON>d<PERSON><PERSON>',
      Messages: 'Üzenet',
      weChat: 'Pillanatok',
      myDynamic: '<PERSON>j<PERSON><PERSON> bejegyz<PERSON>',
      myFavorites: 'Ked<PERSON><PERSON><PERSON>',
      myLiked: 'Kedveltje<PERSON>',
      Hot: 'Trendek',
      Recommend: 'Aj<PERSON>l<PERSON>',
      New: 'Legfrissebb',
      Vote: 'Tippel&Nyerni',
      Game: '2048',
    },
    user: {
      praised: 'Kedvelések szerzése',
      collect: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      chat: 'Csőbbi',
      addFriends: 'Barátok hozzáadása',
      friendRequest: '<PERSON><PERSON>ti kérel<PERSON>',
      verificationMessage: 'Ellenőrző információ',
      sendNow: 'Azonnali küld<PERSON>',
      verificationMessagePlaceholder: 'Hogy vagy! <PERSON><PERSON><PERSON> találkozni.',
    },
    msg: {
      likeCollectTab: 'Kedvelés és kedvenc',
      commentAitTab: "Hozzászólások és {'@'}",
      reply: '<PERSON><PERSON><PERSON><PERSON>',
      likeCond: 'Kedveltem a bejegyzésedet',
      likeConc: 'Tetszett a megjegyzésed',
      likeConr: 'Tetszett a válaszod',
      collectCon: 'Elmentettem a bejegyzésedet',
      commentCon: 'Hozzászólást írtam a bejegyzésedhez',
      replyCon: 'Válaszolt',
      aitCon: "{'@'}neked",
    },
  },
  detail: {
    replyNum: 'Összesen %num% válasz',
    seeMore: 'Továbbiak megtekintése',
    stop: 'Összecsukás',
    originalText: 'Küldés',
    translation: 'Fordítás',
    commentNum: 'hozzászólás',
    send: 'Megerősítés',
    reply: 'Válasz',
    replied: 'Válaszolt',
    //   have:'已有',
    likeListTitle: '%num% barát tetszés',
  },
  add: {
    title: 'Bejegyzés',
    input: 'Kérlek, írd be a bejegyzés tartalmát',
    topic: 'Téma hozzáadása',
    whoSee: {
      title: 'Kinek mutathatom meg',
      all: 'Mindenki',
      friend: 'A barátok láthatják',
      oneself: 'Csak magadnak látható',
    },
    publish: 'Kiadás',
    audioStart: 'Kattints a beszédhez',
    audioEnd: 'Kattints a befejezéshez',
    searchPlaceholder: 'Keresd meg a barátaidat',
    confirm: 'Következő lépés',
    audioPermission: 'Kérlek, először nyisd meg a mikrofon engedélyt',
    imagePermission: 'Kérlek, először engedélyezd a kamera engedélyt',
    aitUser: "{'@'}felhasználó",
  },
  report: {
    next: 'Következő lépés',
    confirmReport: 'Erősítsd meg a jelentést',
    placeholder: 'Kérjük, adja meg a tartalmat',
    detailTitle: 'Részletes leírás',
    imgTitle: 'Kép bizonyíték',
    reportSuccess: 'Jelentés sikeres',
    reportFail: 'A jelentés sikerült',
    reportSuccessInfo:
      'A benyújtás után a platform aktívan ellenőrzi és feldolgozza; köszönjük az erőfeszítést a társadalmi környezet fenntartásáért!',
    reportPublish: 'Vissza',
    reportOneTitleAfterD: 'A %name% bejelentést tesz',
    reportOneTitleAfterC: 'A %name% megjegyzést jelentést tesz be',
    reportOneTitleAfterR: 'A válasz %name%您',
    reportTwoTitle: '%title%-et kiválasztotta',
    reportEndTitleAfterD: 'A %name% post-jelentése %title% tartozik',
    reportEndTitleAfterC:
      'A %name% megjegyzésekre vonatkozó jelentés %title% tartozik',
    reportEndTitleAfterR: 'A %name% válasz jelentése %title% tartozik',
  },
  tooltip: {
    delete: 'Törlése',
    modify: 'Módosítás',
    cancelCollect: 'Eltávolítás a kedvencek közül',
    report: 'Jelentés',
    block: 'Blokkolni',
  },
  delete: {
    deleteCon: 'Biztos vagy benne, hogy törölni akarod ezt a tartalmat?',
    deleteCancel: 'Kép',
    deleteConfirm: 'Megerősítés',
    blockCon: 'Valóban blokkolni szeretné?',
  },
  toast: {
    likeSuccess: 'Sikeres kedvelés',
    likeCancel: 'A kedvelés törölve lett',
    likeFail: 'A kedvelés sikertelen',
    collectSuccess: 'Kedvenc sikeresen hozzáadva',
    collectCancel: 'Nem kedvelt',
    collectFail: 'Kedvencelés sikertelen',
    publishSuccess: 'Bejegyzés sikeres',
    publishFail: 'Bejegyzés sikertelen',
    modifySuccess: 'Módosítás sikeres',
    topicInfo: 'Legfeljebb 5 témát választhatsz',
    aitInfo: "Legfeljebb {'@'}5 felhasználó",
    ait: "Kérlek, írj be legalább 1 karaktert {'@'} előtt",
    dynamicInput:
      'Tölts fel legalább egyet a következőkből: dinamikus tartalom, képek vagy videók',
    nextInfo: 'Kérlek, válassz először egy lehetőséget',
    reportSuccess: 'Jelentés sikeres',
    reportFail: 'A jelentés sikerült',
    audioTextSuccess: 'Hangról szövegre sikeres',
    audioTextFail: 'Hangról szövegre sikertelen',
    translationSuccess: 'Fordítás sikeres',
    translationFail: 'Fordítás sikertelen',
    uploadImageFail: 'Feltöltés sikertelen',
    deleteSuccess: 'Törlés sikeres',
    deleteFail: 'Törlés sikertelen',
    // 新加
    imageLimit: 'A fájl mérete nem haladhatja meg a %num%MB-ot',
    imageNum: 'Legfeljebb 9 kép feltölthető',
    uploadPrompt: 'Koppintson a képek és videók feltöltéséhez',
    filePrompt:
      '(Doc, DOCX és PDF fájlformátumok támogatják, és a fájlméret nem haladhatja meg az 5 MB)',
    imageBefore: 'Legfeljebb 4 MB egy kép esetében',
    imageShowToast: 'Túl nagy a feltöltési fájl',
    audioFail: 'Hiba a felvétel befejezésekor',
    collectCancelFail: 'Lemondás sikertelen',
    collectCancelSuccess: 'Lemondás sikeres',
    dynamicFail: 'A bejegyzés nem létezik',
    addCommentViolation:
      'The content you submitted is suspected of violating regulations, please modify and resubmit. Modify and resubmit',
    addCommentFail: 'Failed to add comment',
    addReplyFail: 'Failed to add reply',
    addDynamicViolation:
      'A benyújtott "bejegyzés" tartalma szabályszegés gyanúja miatt módosításra szorul, kérjük, módosítsa és nyújtsa be újra.',
    addTopicViolation:
      'A benyújtott "téma" tartalma szabályszegés gyanúja miatt módosításra szorul, kérjük, módosítsa és nyújtsa be újra.',
    addImageViolation:
      'A benyújtott "kép" tartalma szabályszegés gyanúja miatt módosításra szorul, kérjük, módosítsa és nyújtsa be újra.',
    topicCon: 'A téma tartalma nem lehet üres',
    getMsgFail: 'Nem sikerült információt lekérni',
    loginFail: 'A bejelentkezés nem sikerült',
    aitInfoPermission: 'Jelenleg csak magad számára látható',
    alreadyReport:
      'Többször jelentetted, kérlek várj a platform visszajelzésére',
    commentAfterDelete: 'A megjegyzés törölve lett',
    replyAfterDelete: 'A válasz törölve lett',
    msgDataListFail: 'Nem sikerült lekérdezni az adatokat',
    videoLimit: 'A videó nem lehet nagyobb 25 MB-nál',
    videoPrompt: 'Maximum 1 videót tölthetsz fel.',
    videoToast: 'Csak képeket vagy videókat tölthetsz fel.',
    imageTitle: 'Kép feltöltése',
    videoTitle: 'Videó feltöltése',
    applySuccess: 'Kérelem sikeres küldése',
    applyFail: 'Kérelem küldése sikertelen',
    blacklistPrompt: 'A feketelistáról nem lehet barátot hozzáadni',
    friendNumPrompt: 'A másik felhasználó barátainak száma teljes',
    myNumPrompt: 'Az aktuális barátainak száma teljes',
    failedPrompt: 'Paraméterhiba',
    alignPrompt:
      'Már hozzáadtad ezt a személyt barátnak, nem lehet újra küldeni a kérelmet',
    applyMyPrompt: 'Nem lehet magadat hozzáadni',
    alignApply:
      'Már elküldtél egy baráti kérelmet. 48 óra múlva újra elküldhető',
    blockSuccess: 'A felhasználó hozzáadva a visszaírási listához',
    blockFail: 'A blokkolás sikertelen',
    blockListFull: 'A blokkolási lista tele van',
    checkAgreementPrompt:
      'Ön nem járul hozzá a 《Tartalom közzétételi nyilatkozat》 adatlaphoz, és nem tehet közzé információkat',
    AgreementFile: 'Elolvasta és elfogadta a dokumentumot',
    fileTitle: '《Tartalom közzétételi nyilatkozat》',
    sameLanguagePrompt:
      'Jelenleg ugyanazon nyelven van, fordítás nem szükséges',
  },
  vote: {
    voteProgress: 'Folyamatban',
    voteEnd: 'Befejeződött',
    voteSettle: 'Elszámolva',
    oneselfNum: 'Szavazott',
    voteNum: '{num} Érme',
    timeName: 'Maradék idő',
    allNum: 'Összes érme szám',
    participateInVoting: 'Összesen játékosok száma:',
    getCoins: 'Ezúttal kapott {num} érmét',
    voteBtn: 'Választ',
    voteTitle: 'Szavazás a {num} mellett',
    inputInfo: 'Válassza ki a mennyiséget',
    voteConfirm: 'Megerősítése',
    voteSuccess: 'Sikeres',
    voteFail: 'Sikertelen',
    statusEnd: 'Az esemény befejeződött',
    voteTnfo: 'Az eseményben való részvételhez minimálisan 1 érme szükséges',
    hold: 'Tárgyal',
    balance: 'Az aktuális fiók saldo nem elegendő. Kérjük, töltse fel időben',
    remainingTimeData:
      '{days} nap {hours} óra {minutes} perc {seconds} másodperc',
    remainingTimeDaysHours: '{days} nap {hours} óra',
    questionInfo:
      'Az összes felhasználói érme az esemény jutalmi medencéjébe kerül, és azoknak a felhasználóknak, akik helyesen tippelnek, a tippelt érmék számának megfelelően osztják szét a jutalmi medencében lévő összes 【idő-tér érmet】.',
  },
  video: {
    videoIndex: 'Mondj valamit...',
    videoDetail: 'Részletek oldala',
    videoTitle: 'Videó',
  },
  match: {
    statusProgress: 'Folyamatban',
    statusEnd: 'Bejelezve',
    matchCon: 'Verseny tartalma',
    matchRule: 'Verseny szabályai',
    matchAward: 'Verseny jutalma',
    matchInstructions: 'Verseny irányelvek',
    matchWorks: 'Termelés',
    matchTitle: 'Verseny',
    matchTime: 'Regisztrációs időszak',
    myMatch: 'Versenyeim',
    statusWaiting: 'Várakozás indításra',
    vote: 'Szavazás',
    voted: 'Szavazott',
    voteNum: '{num} szavazás',
    uploadWorks: 'Töltsd fel munkáját',
    matchAddTitle: 'Cím',
    titlePlaceholder: 'Kérjük, adjon meg egy címet',
    submit: 'Beküldés',
    content: 'Tartalom',
    voteConfirm: 'Szeretnél szavazni {name} műveért?',
    voteSuccess: 'Szavazás sikeres',
    voteFail: 'Szavazás sikertelen',
    imageShowToast: 'Cím és kép nem lehet üres',
    videoShowToast: 'Cím és videó nem lehet üres',
    addWorkTitle:
      'A beküldött "cím" tartalma sértésre van gyanúsítva. Kérjük, módosítsa és újra küldje be.',
    addWorkContent:
      'A beküldött "tartalom" tartalma sértésre van gyanúsítva. Kérjük, módosítsa és újra küldje be.',
    againAddWork:
      'Ön már részt vett az aktuális versenyben és nem vehet részt újra!',
    myMatchTitle: 'Végső műveim',
    otherMatchTitle: 'Mások művei',
  },
  empty: {
    comment: 'Még nincsenek megjegyzések',
    list: 'Nincs elérhető adat',
    content: 'Nincs elérhető tartalom',
    message: 'Nincsenek elérhető üzenetek',
  },
  game:{
    myCardTitle:`A kártyáim`,
    merge:`Összefűzés`,
    maskObtaining:`Gratulálok a nyereményhez`,
    maskInfo:`Gyorsan és adjon hozzá barátokat, hogy új kártyákat szintetizáljon!`,
    clickAnywhere:`Kattintson bárhová a folytatáshoz`,
    mergeSuccess:`A szintézis sikeres`,
    newFriends:`Új barát`,
    receiveTitle:`Gratulálok!`,
    receiveInfo:`A sikeres állítás után a 2048 eltűnik!`,
    receiveImgText:`Teleportálási kártya`,
    receivePublish:`Menjen a levelezőbe jutalom igényléséhez`,
    shop:`Központi központi`,
    purchaseCards:`Vásárlási kártyák`,
    totalAmount:`Összes összeg`,
    purchase:`Vásárlás`,
    selectCards:`Válasszon kártyákat`,
    purchaseSuccessful:`Vásárlás sikeres`,
    purchaseFailed:`A vásárlás nem sikerült`,
    synthesisFailed:`A szintézis nem sikerült`,
    claimSuccessful:`Sikeresű kérelem`,
    claimFailed:`A követelés nem sikerült`,
    exchangeTips:`2048-as kártyád van! Váltsd be egy teleport kártyáért!`,
    rule:`Játék szabályai`,
    ruleNum1:`1.A játék elkezdése után véletlenszerűen kapsz egy színes 2 alapkártyát`,
    ruleNum2:`2.Csatlakozz barátokkal más felhasználókkal, akik ugyanazon számú, különböző színű kártyákkal rendelkeznek, és fejlettebb kártyákat készíthetsz`,
    ruleNum3:`3.A sikeres szintézis után a két számla eredeti számú kártyáját a kétszeres számú új kártyára frissítik`,
    ruleNum4:`4.Ha a kártya száma eléri a 2048-at, akkor válthatja ki az átviteli kártyát.`,
    ruleNum5:`5.A szintézis kudarcának a következő okai vannak: a frissített kártya színét és a másik színét választja; a kártyát már mások használták; Ön már barátságos kapcsolatban van egymással; A barátok száma elérte a határt; A barátok száma elérte a határt.`
  }
}

export default zh_cn
