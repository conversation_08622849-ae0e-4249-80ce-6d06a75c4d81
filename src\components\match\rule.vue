<template>
  <div class="item">
    <div class="text-title">
      <span>{{ title }}</span>
    </div>
    <div class="text-content" v-html="data"></div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  data: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.item {
  width: 100%;
  height: 100%;

  .text-title {
    text-align: center;
    font-size: 42px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20px;
    width: 100%;
  }

  .text-content {
    background: #ffffff;
    border-radius: 4px;
    padding: 42px 62px;

    // 为富文本内容添加样式规则
    :deep(h1) {
      font-size: 36px;
      line-height: 1.5;
      margin-top: 20px;
      margin-bottom: 16px;
      font-weight: 500;
      color: #333333;
    }

    :deep(h2) {
      font-size: 32px;
      line-height: 1.5;
      margin-top: 18px;
      margin-bottom: 14px;
      font-weight: 500;
      color: #333333;
    }

    :deep(h3) {
      font-size: 28px;
      line-height: 1.5;
      margin-top: 16px;
      margin-bottom: 12px;
      font-weight: 500;
      color: #333333;
    }

    :deep(h4) {
      font-size: 26px;
      line-height: 1.5;
      margin-top: 14px;
      margin-bottom: 10px;
      font-weight: 500;
      color: #333333;
    }

    :deep(h5, h6) {
      font-size: 24px;
      line-height: 1.5;
      margin-top: 12px;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333333;
    }

    :deep(p) {
      font-size: 26px;
      line-height: 1.6;
      margin-bottom: 16px;
      color: #333333;
      word-wrap: break-word;
    }

    :deep(ul, ol) {
      padding-left: 30px;
      margin-bottom: 16px;

      li {
        font-size: 26px;
        line-height: 1.6;
        margin-bottom: 8px;
        color: #333333;
      }
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
      margin: 16px 0;
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;

      th,
      td {
        border: 1px solid #ddd;
        padding: 12px;
        font-size: 26px;
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
