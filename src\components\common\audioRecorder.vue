<template>
  <div>
    <van-popup
      class="popupAudio"
      v-model:show="dialogVisible"
      round
      @close="handleClose('close')"
    >
      <div class="isloading" v-if="isLoading">
        <van-loading type="spinner" color="#ffffff" />
      </div>
      <div class="audio" v-else>
        <!-- 录音前 -->
        <!-- 录音中 -->
        <div class="prompt" v-if="isRecording">
          <!-- {{ circleText }} -->
          <div>{{ $t('add.audioEnd') }}</div>
        </div>
        <div class="prompt" v-else>{{ $t('add.audioStart') }}</div>
        <!-- @touchstart="touchStart" @touchend="touchEnd" -->
        <van-circle
          v-model:current-rate="currentRate"
          :rate="rate"
          :speed="100"
          @click="startAudio"
        >
          <template #default>
            <!-- 这里使用插槽来自定义内容 -->
            <img class="circleImg" src="@/assets/images/index/mike.png" />
          </template>
        </van-circle>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { showToast } from 'vant'
// 必须引入的核心
import Recorder from 'recorder-core'
//引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引入进来即可
import 'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine'
//录制wav格式的用这一句就行
//import 'recorder-core/src/engine/wav'

//可选的插件支持项，这个是波形可视化插件
import 'recorder-core/src/extensions/waveview'
import { ref } from 'vue'
//ts import 提示：npm包内已自带了.d.ts声明文件（不过是any类型）
import { rcvaudio } from '@/api/translation.js'
import useCounterStore from '@/stores/counter'
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
// const { t } = useI18n();
export default {
  props: ['isShowAudio'],
  data() {
    return {
      dialogVisible: false,
      isRecording: false,
      currentRate: 0,
      rate: 1,
      countdown: 30,
      timer: null,
      isLoading: false,
      blob: null, // 你的 blob 对象
      localUrl: null, // 你的 localUrl
      duration: 0, // 你的录音时长
      countStore: useCounterStore(),
    }
  },
  computed: {
    circleText() {
      return `${this.countdown}s`
    },
  },
  watch: {
    isShowAudio(n, o) {
      console.log(n)
      this.dialogVisible = n
      if (this.dialogVisible) {
        // 创建录音对象
        this.recOpen()
      }
    },
  },
  methods: {
    startAudio() {
      if (this.isRecording) {
        this.touchEnd()
      } else {
        // 开始录音
        this.touchStart()
      }
    },
    // 触摸开始
    touchStart() {
      // showToast("触摸开始了")
      this.isRecording = true
      this.startCountdown()
      this.recStart()
    },
    // 触摸结束
    touchEnd() {
      this.isRecording = false
      this.stopCountdown()
      this.isLoading = true
    },
    startCountdown() {
      if (this.timer) return
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
          this.rate += 3.3334
        } else {
          this.stopCountdown()
        }
      }, 1000)
    },
    stopCountdown() {
      clearInterval(this.timer)
      this.timer = null
      this.currentRate = this.rate
      this.countdown = 30
      // 结束录音
      this.recStop()
      this.sound == 'before'
    },
    handleClose() {
      this.dialogVisible = false
      console.log('关闭handleClose')
      this.$emit('dialogCloseAudio', false)
      // 判断是否是录音中
      if (this.isRecording) {
        this.recStop('pause')
        this.stopCountdown()
      }
      this.isRecording = false
      this.rate = 1
      this.isLoading = false
    },
    recOpen() {
      //创建录音对象
      this.rec = Recorder({
        type: 'mp3', //录音格式，可以换成wav等其他格式
        sampleRate: 16000, //录音的采样率，越大细节越丰富越细腻
        bitRate: 16, //录音的比特率，越大音质越好
        onProcess: (
          buffers,
          powerLevel,
          bufferDuration,
          bufferSampleRate,
          newBufferIdx,
          asyncEnd,
        ) => {
          //录音实时回调，大约1秒调用12次本回调
          //可实时绘制波形，实时上传（发送）数据
          if (this.wave)
            this.wave.input(
              buffers[buffers.length - 1],
              powerLevel,
              bufferSampleRate,
            )
        },
      })

      //打开录音，获得权限
      this.rec.open(
        () => {
          console.log('录音已打开')
          if (this.$refs.recwave) {
            //创建音频可视化图形绘制对象
            this.wave = Recorder.WaveView({ elem: this.$refs.recwave })
          }
        },
        (msg, isUserNotAllow) => {
          //用户拒绝了录音权限，或者浏览器不支持录音
          console.log(
            (isUserNotAllow ? 'UserNotAllow，' : '') + '无法录音:' + msg,
          )
          // showToast("无法录音:" + msg)
          // showToast("请检查是否打开麦克风权限")
        },
      )
    },
    recStart() {
      if (!this.rec) {
        console.error('未打开录音')
        return
      }
      this.rec.start()
      console.log('已开始录音')
    },
    recStop(data) {
      if (!this.rec) {
        console.error('未打开录音')
        return
      }
      this.rec.stop(
        (blob, duration) => {
          //blob就是我们要的录音文件对象，可以上传，或者本地播放
          this.recBlob = blob
          //简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
          var localUrl = (window.URL || webkitURL).createObjectURL(blob)
          console.log('录音成功', blob, localUrl, '时长:' + duration + 'ms')
          // this.playAudio()
          if (data == 'pause') {
          } else {
            // 调用语音转文字接口
            this.audioTOtext(blob)
          }
          //试听
          // this.recPlay()
          // rcvaudio(data)
          //     .then((res) => {
          //         // 接口调用成功之后的操作
          //         console.log(res)
          //     })
          //     .catch((err) => {
          //         // 接口调用失败之后的操作
          //         console.log(err)
          //
          //     })
          // this.upload(blob);//把blob文件上传到服务器

          this.rec.close() //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
          this.rec = null
        },
        (err) => {
          console.error('结束录音出错：' + err)
          showToast(this.$t('toast.audioFail'))
          this.rec.close() //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
          this.rec = null
        },
      )
    },
    audioTOtext(data) {
      const formData = new FormData()
      formData.append('file', data, 'filename.wav')
      console.log(formData)
      // const query = {
      //   file: formData,
      //   userid: "1023643775676710912",
      //   token: "MTAyMzY0Mzc3NTY3NjcxMDkxMjoxNzI5ODIxODYz",
      // }
      // console.log(query)
      // 添加参数
      formData.append('userid', this.countStore.loginData.avatarId)
      formData.append('token', this.countStore.apiToken)
      // formData.append("userid", '1050872425710878720');
      // formData.append("token", 'MTA1MDg3MjQyNTcxMDg3ODcyMDoxNzMxMzkwODMz');
      rcvaudio(formData)
        .then((res) => {
          // 接口调用成功之后的操作
          console.log(res)
          if (res.code == 200) {
            // 传递给父组件
            // this.handleClose()
            // res.result
            this.isLoading = false
            this.$emit('audioSuccess', res.result)
            this.$emit('dialogCloseAudio', false)
            showToast(this.$t('toast.audioTextSuccess'))
            // showToast('语音转文字成功')
          } else {
            // showToast('语音转文字失败')
            showToast(this.$t('toast.audioTextFail'))
            this.handleClose()
          }
        })
        .catch((err) => {
          // 接口调用失败之后的操作
          console.log(err)
          // showToast('语音转文字失败')
          showToast(this.$t('toast.audioTextFail'))
          this.handleClose()
        })
    },
    playAudio() {
      // 设置 audio 元素的 src 属性
      this.$refs.audioPlayer.src = this.localUrl

      // 播放音频
      this.$refs.audioPlayer.play()
    },
    upload(blob) {
      //使用FormData用multipart/form-data表单上传文件
      //或者将blob文件用FileReader转成base64纯文本编码，使用普通application/x-www-form-urlencoded表单上传
      var form = new FormData()
      form.append('upfile', blob, 'recorder.mp3') //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
      form.append('key', 'value') //其他参数

      var xhr = new XMLHttpRequest()
      xhr.open('POST', '/upload/xxxx')
      xhr.onreadystatechange = () => {
        if (xhr.readyState == 4) {
          if (xhr.status == 200) {
            console.log('上传成功')
          } else {
            console.error('上传失败' + xhr.status)
          }
        }
      }
      xhr.send(form)
    },
    recPlay() {
      //本地播放录音试听，可以直接用URL把blob转换成本地播放地址，用audio进行播放
      var localUrl = URL.createObjectURL(this.recBlob)
      // var audio = document.createElement("audio");
      // audio.controls = true;
      // document.body.appendChild(audio);
      // audio.src = localUrl;
      this.$refs.audioPlayer.src = localUrl

      // 播放音频
      this.$refs.audioPlayer.play()
      // audio.play(); //这样就能播放了

      //注意不用了时需要revokeObjectURL，否则霸占内存
      // setTimeout(function () { URL.revokeObjectURL(audio.src) }, 5000);
    },
  },
  beforeDestroy() {
    this.stopCountdown()
  },
  created() {},
  mounted() {},
}
</script>
<style scoped lang="scss">
.popupAudio {
  width: 300px !important;
  height: 240px;
  background: rgba(3, 10, 19, 0.8);
  border-radius: 10px;

  .isloading {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .audio {
    display: flex;
    flex-direction: column;
    align-items: center;

    .van-circle {
      width: 100px;
      height: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 34px 0 20px;

      .circleImg {
        width: 74px;
        height: 74px;
      }
    }

    .prompt {
      font-size: var(--size_24);
      line-height: 35px;
      color: #ffffff;
      padding-top: 20px;
      text-align: center;
    }
  }
}

:deep(.van-circle__text) {
  font-size: 30px;
}
</style>
