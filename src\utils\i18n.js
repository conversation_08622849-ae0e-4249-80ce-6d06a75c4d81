export function i18nType(data) {
  switch (data) {
    case 'ChineseSimplified':
      return 'zh_cn'
    case 'English':
      return 'en_us'
    case 'Korean':
      return 'ko'
    case 'Japanese':
      return 'jpn'
    case 'German':
      return 'ge'
    case 'Spanish':
      return 'sp'
    case 'Italian':
      return 'it'
    case 'French':
      return 'fr'
    case 'Portuguese':
      return 'pt'
    case 'Russian':
      return 'ru'
    case 'Dutch':
      return 'nl'
    case 'Hindi':
      return 'hi'
    case 'Bengali':
      return 'bn'
    case 'Hungarian':
      return 'hu'
    case 'Turkish':
      return 'tr'
    case 'Vietnamese':
      return 'vi'
    case 'Indonesian':
      return 'id'
    default:
      return {} // 默认返回空对象或错误处理逻辑
  }
}
