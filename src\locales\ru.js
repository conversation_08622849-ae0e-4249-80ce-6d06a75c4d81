const ru = {
  index: {
    title: 'Главная страница',
    menuItems: {
      Dynamic: 'Посты',
      Favorites: 'Собрать',
      Liked: 'Понравилось',
      Messages: 'Сообщение',
      weChat: 'Моменты',
      myDynamic: 'Мои посты',
      myFavorites: 'Мои избранные',
      myLiked: 'Мои лайки',
      Hot: 'Тренды',
      Recommend: 'Рекомендовать',
      New: 'Последний',
      Vote: 'Угадать&Получить',
      Game: '2048',
    },
    user: {
      praised: 'Получить лайки',
      collect: 'Собрать',
      chat: 'Чат',
      addFriends: 'Добавить друзей',
      friendRequest: 'Заявка на дружбу',
      verificationMessage: 'Информация для проверки',
      sendNow: 'Отправить немедленно',
      verificationMessagePlaceholder: 'Каких друзей вы хотите найти?',
    },
    msg: {
      likeCollectTab: 'Лайкнуть и добавить в избранное',
      commentAitTab: "Комментарии и {'@'}",
      reply: 'Ответить',
      likeCond: 'Мне понравился твой пост',
      likeConc: 'Понравился ваш комментарий',
      likeConr: 'Понравился ваш ответ',
      collectCon: 'Я сохранил твой пост',
      commentCon: 'Прокомментировал твой пост',
      replyCon: 'Ответил',
      aitCon: "{'@'}вам",
    },
  },
  detail: {
    replyNum: 'Всего %num% ответов',
    seeMore: 'Смотреть больше',
    stop: 'Свернуть',
    originalText: 'Оригинальный текст',
    translation: 'Перевести',
    commentNum: 'комментариев',
    send: 'Отправить',
    reply: 'Ответить',
    replied: 'Ответил',
    //   have:'已有',
    likeListTitle: 'Лайки от %num%  друзей',
  },
  add: {
    title: 'Опубликовать',
    input: 'Пожалуйста, введите содержимое поста',
    topic: 'Добавить тему',
    whoSee: {
      title: 'Кому я могу это показать',
      all: 'Всем',
      friend: 'Друзья могут видеть',
      oneself: 'Только вам видно',
    },
    publish: 'Опубликовать',
    audioStart: 'Нажмите, чтобы говорить',
    audioEnd: 'Нажмите, чтобы закончить',
    searchPlaceholder: 'Искать своих друзей',
    confirm: 'Подтвердить',
    audioPermission: 'Пожалуйста, сначала откройте разрешение на микрофон',
    imagePermission: 'Пожалуйста, сначала включите разрешения на камеру',
    aitUser: "{'@'}пользователь",
  },
  report: {
    next: 'Следующий шаг',
    confirmReport: 'Подтвердите отчет',
    placeholder: 'Пожалуйста, введите содержимое',
    detailTitle: 'Подробное описание',
    imgTitle: 'Доказательства изображения',
    reportSuccess: 'Отчет успешен',
    reportFail: 'Отчет не удался',
    reportSuccessInfo:
      'После отправки платформа активно проверит и обработает; спасибо за ваши усилия по поддержанию социальной среды!',
    reportPublish: 'Назад',
    reportOneTitleAfterD: 'вы сообщаете о сообщении %name%',
    reportOneTitleAfterC: 'Вы сообщаете о комментарии %name%',
    reportOneTitleAfterR: 'вы сообщаете об ответе %name%',
    reportTwoTitle: 'Вы выбрали %title%',
    reportEndTitleAfterD: 'ваш отчет о посту %name% принадлежит %title%',
    reportEndTitleAfterC: 'ваш отчет о комментариях %name% принадлежит %title%',
    reportEndTitleAfterR: 'ваш отчет о ответах %name% принадлежит %title%',
  },
  tooltip: {
    delete: 'Удалить',
    modify: 'Изменить',
    cancelCollect: 'Убрать из избранного',
    report: 'Сообщить',
    block: 'Заблокировать',
  },
  delete: {
    deleteCon: 'Вы уверены, что хотите удалить этот контент?',
    deleteCancel: 'Отменить',
    deleteConfirm: 'Подтвердить',
    blockCon: 'Вы уверены, что хотите заблокировать?',
  },
  toast: {
    likeSuccess: 'Успешно понравилось',
    likeCancel: 'Лайк был отменен',
    likeFail: 'Не удалось поставить лайк',
    collectSuccess: 'Избранное успешно',
    collectCancel: 'Убрано из избранного',
    collectFail: 'Не удалось добавить в избранное',
    publishSuccess: 'Пост успешно',
    publishFail: 'Не удалось опубликовать пост',
    modifySuccess: 'Изменение успешно',
    topicInfo: 'Вы можете выбрать до 5 тем',
    aitInfo: "До {'@'}5 пользователей",
    ait: "Пожалуйста, введите хотя бы 1 символ перед {'@'}",
    dynamicInput:
      'Загрузите, пожалуйста, по крайней мере один из следующих элементов: динамический контент, изображения или видео',
    nextInfo: 'Пожалуйста, сначала выберите один вариант',
    reportSuccess: 'Отчет успешен',
    reportFail: 'Отчет не удался',
    audioTextSuccess: 'Преобразование голоса в текст успешно',
    audioTextFail: 'Преобразование голоса в текст не удалось',
    translationSuccess: 'Перевод успешен',
    translationFail: 'Перевод не удался',
    uploadImageFail: 'Ошибка загрузки',
    deleteSuccess: 'Удаление успешно',
    deleteFail: 'Удаление не удалось',
    // 新加
    imageLimit: 'Размер файла не может превышать %num%МБ',
    imageNum: 'можно загрузить до 9 изображений',
    uploadPrompt: 'Пожалуйста, нажмите, чтобы загрузить картинку и видео',
    filePrompt:
      '(Поддерживаются форматы файлов Doc, docx и pdf, размер файла не может превышать 5 мб)',
    imageBefore: 'не более 4 МБ для одного изображения',
    imageShowToast: 'Загруженный файл слишком большой',
    audioFail: 'Ошибка завершения записи',
    collectCancelFail: 'Отмена не удалась',
    collectCancelSuccess: 'Отмена успешна',
    dynamicFail: 'Пост не существует',
    addCommentViolation:
      'Содержимое, которое вы представили, подозревается в нарушении правил, пожалуйста, измените и отправьте снова. Измените и отправьте снова.',
    addCommentFail: 'Не удалось добавить комментарий',
    addReplyFail: 'Не удалось добавить ответ',
    addDynamicViolation:
      'Содержимое "поста", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
    addTopicViolation:
      'Содержимое "темы", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
    addImageViolation:
      'Содержимое "изображения", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
    topicCon: 'Содержимое темы не может быть пустым',
    getMsgFail: 'Не удалось получить информацию',
    loginFail: 'Ошибка входа',
    aitInfoPermission: 'В данный момент видно только вам',
    alreadyReport:
      'Вы сообщили несколько раз, пожалуйста, дождитесь обратной связи от платформы',
    commentAfterDelete: 'Комментарии удалены',
    replyAfterDelete: 'Ответ удален',
    msgDataListFail: 'Ошибка получения данных',
    videoLimit: 'Видео не должно превышать 25 МБ',
    videoPrompt: 'Можно загрузить не более 1 видео.',
    videoToast: 'Можно загружать только изображения или видео.',
    imageTitle: 'Загрузить изображение.',
    videoTitle: 'Загрузить видео.',
    applySuccess: 'Заявка успешно отправлена',
    applyFail: 'Не удалось отправить заявку',
    blacklistPrompt: 'Нельзя добавлять друзей из черного списка',
    friendNumPrompt:
      'Количество друзей у другого пользователя уже достигло максимума',
    myNumPrompt: 'Количество текущих друзей уже достигло максимума',
    failedPrompt: 'Ошибка параметра',
    alignPrompt:
      'Вы уже добавили этого человека в друзья, повторная заявка невозможна',
    applyMyPrompt: 'Нельзя добавить себя в друзья',
    alignApply:
      'Ты уже отправил запрос на дружбу. Сможешь отправить еще один через 48 часов',
    blockSuccess: 'Пользователь был добавлен в черный список',
    blockFail: 'Блокировка не удалась',
    blockListFull: 'Список блокировки полон',
    checkAgreementPrompt:
      'Вы не согласны с 《Объявление о публикации контента》 и не можете опубликовать динамику',
    AgreementFile: 'Вы прочитали и согласились с этим документом',
    fileTitle: '《Объявление о публикации контента》',
    sameLanguagePrompt:
      'В настоящее время используется один и тот же язык, перевод не требуется',
  },
  vote: {
    voteProgress: 'В процессе',
    voteEnd: 'Завершено',
    voteSettle: 'Оплачено',
    oneselfNum: 'Голосовал',
    voteNum: '{num} Монета',
    timeName: 'Оставшееся время',
    allNum: 'Общее количество монет',
    participateInVoting: 'Общее количество игроков:',
    getCoins: 'В этот раз вы заработали {num} монет',
    voteBtn: 'Выбирать',
    voteTitle: 'Голосовать за {num}',
    inputInfo: 'Пожалуйста, выберите количество',
    voteConfirm: 'Подтвердить',
    voteSuccess: 'Успех',
    voteFail: 'Неудача',
    statusEnd: 'Событие завершено',
    voteTnfo:
      'Минимальное количество монет, необходимых для участия в мероприятии, составляет 1',
    hold: 'Иметь',
    balance:
      'Ваш текущий баланс недостаточен. Пожалуйста, пополните счет вовремя',
    remainingTimeData: '{days} дней {hours} часов {minutes} мин {seconds} сек',
    remainingTimeDaysHours: '{days} дней {hours} часов',
    questionInfo:
      'Все монеты пользователя пойдут в призовой фонд этого мероприятия, и пользователи, которые правильно угадают, поделят все 【тайм-спейс монеты】 в призовом фонде в соответствии с количеством угаданных монет.',
  },
  video: {
    videoIndex: 'Скажите что-нибудь...',
    videoDetail: 'Страница деталей',
    videoTitle: 'Видео',
  },
  match: {
    statusProgress: 'В процессе',
    statusEnd: 'Завершено',
    matchCon: 'Содержание соревнования',
    matchRule: 'Правила соревнования',
    matchAward: 'Награда за соревнование',
    matchInstructions: 'Руководство по конкуренции',
    matchWorks: 'Производство',
    matchTitle: 'Конкуренция',
    matchTime: 'Время регистрации',
    myMatch: 'Мои соревнования',
    statusWaiting: 'Ожидание начала',
    vote: 'Голосование',
    voted: 'Голосовал',
    voteNum: '{num} голосов',
    uploadWorks: 'Загрузите свою работу',
    matchAddTitle: 'Заголовок',
    titlePlaceholder: 'Пожалуйста, введите заголовок',
    submit: 'Подача',
    content: 'Содержание',
    voteConfirm: 'Хотите проголосовать за произведение{name}?',
    voteSuccess: 'Голосование прошло успешно',
    voteFail: 'Голосование неудачное',
    imageShowToast: 'Заголовок и изображение не могут быть пустыми',
    videoShowToast: 'Заголовок и видео не могут быть пустыми',
    addWorkTitle:
      'Поданный "заголовок" подозревается в нарушении правил. Пожалуйста, внесите изменения и отправьте снова.',
    addWorkContent:
      'Поданный "контент" подозревается в нарушении правил. Пожалуйста, внесите изменения и отправьте снова.',
    againAddWork:
      'Вы уже участвовали в текущем соревновании и не можете принять участие снова!',
    myMatchTitle: 'Мои произведения',
    otherMatchTitle: 'Произведения других',
  },
  empty: {
    comment: 'Комментариев пока нет',
    list: 'Данные недоступны',
    content: 'Содержимое недоступно',
    message: 'Нет доступных сообщений',
  },
  game:{
    myCardTitle:`мои карты.`,
    merge:`Синтезировать`,
    maskObtaining:`Поздравляю с победой!`,
    maskInfo:`Поторопитесь и добавьте друзей, чтобы синтезировать новые карты!`,
    clickAnywhere:`Нажмите в любом месте, чтобы продолжить`,
    mergeSuccess:`Успешный синтез`,
    newFriends:`новый друг.`,
    receiveTitle:`Поздравляем!`,
    receiveInfo:`После успешной претензии 2048 исчезнет!`,
    receiveImgText:`Телепорт-карта`,
    receivePublish:`Перейдите в почтовый ящик, чтобы получить вознаграждение`,
    shop:`Торговый центр`,
    purchaseCards:`Карты для покупки`,
    totalAmount:`Общая сумма`,
    purchase:`Покупка`,
    selectCards:`Выберите пункт карты`,
    purchaseSuccessful:`Покупка прошла успешно`,
    purchaseFailed:`Покупка не состоялась`,
    synthesisFailed:`Синтез не удалось синтезировать`,
    claimSuccessful:`Заявка успешна`,
    claimFailed:`Претензия не удовлетворена`,
    exchangeTips:`У тебя карта 2048! Используйте его на телепортную карту!`,
    rule:`Правила игры`,
    ruleNum1:`После начала игры вы случайно получите базовую карту с цветом 2`,
    ruleNum2:`Добавьте друзей к другим пользователям с одинаковыми номерами и картами разных цветов, которые могут синтезировать карты более высокого уровня`,
    ruleNum3:`После успешного синтеза карты с двумя исходными номерами счетов были обновлены до новых карт с удвоенным числом`,
    ruleNum4:`Когда число на карте достигает 2048, вы можете обменять карту на одну.`,
    ruleNum5:`Существует несколько причин неудачи синтеза: вы выбираете обновленный цвет карты и тот же цвет, что и другой; Карточки уже используются другими людьми; Вы и ваш друг уже друзья; Количество ваших друзей достигло верхнего предела; Количество друзей друг друга достигло верхнего предела.`
  }
}

export default ru
