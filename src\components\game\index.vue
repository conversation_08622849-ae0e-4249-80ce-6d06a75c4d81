<template>
  <div class="game-container">
    <!-- 我的卡牌区域 -->
    <div class="my-cards">
      <div class="title">{{ $t('game.myCardTitle') }}：</div>
      <div class="cards-row" v-if="myCards.length > 0">
        <div class="cards-row-item" v-for="(card, index) in myCards" :key="'my-card-' + index"
          @click="selectCord(card)">
          <GameCard :color="card.cardColor" :number="card.cardNumber" :id="card.id" cardType="my" />
          <!--  @openReceiveMask="openReceiveMask" -->
          <!-- <GameCard v-for="(card, index) in 8" :key="'my-card-' + index" color="1"
          number="18"  cardType="my" @openReceiveMask="openReceiveMask" /> -->
          <div class="selected-overlay" v-if="selectedItem === card"></div>
        </div>

      </div>
      <div class="card-action refresh">
        <img src="@/assets/images/game/question.webp" @click="handleGameRule" alt="问号" />
        <img src="@/assets/images/game/reflesh.webp" @click="getCardList" alt="刷新" />
      </div>
    </div>

    <!-- 投票项目列表 -->
    <div class="vote-list" v-if="voteItems.length > 0">
      <div class="vote-item" v-for="(item, index) in voteItems" :key="'vote-item-' + index">
        <div class="user-info">
          <div class="avatar-wrapper">
            <!-- <img class="avatar" :src="item.avatar" alt="" /> -->
            <avatar :url="item.avatar" :userId="item.userId"></avatar>
          </div>
          <div class="user-details">
            <div class="user-name">
              <div class="name">{{ item.nickName }}</div>
              <img v-if="item.sex == '0'" src="@/assets/images/game/men_icon.webp" alt="" />
              <img v-else src="@/assets/images/game/women_icon.webp" alt="" />
            </div>
            <div class="user-meta">
              <country :code="item.countryCode" />
              <div class="birth-date">
                <img src="@/assets/images/game/coke-icon.webp" alt="" />
                {{ item.birthday }}
              </div>
            </div>
          </div>
          <GameCard :color="item.cardColor" :number="item.cardNumber" cardType="my" />
          <!-- 合成按钮 -->
          <div class="action-button">
            <van-button class="combine-btn" :disabled="disabledButton" @click="openSelectCord(item)">
              {{ $t('game.merge') }}
            </van-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="no-data">
      <Empty :title="$t('empty.content')" />
    </div>
    <div class="affix" @click="addShop">
      <img src="@/assets/images/game/game-icon.webp" />
    </div>
    <Rule :dialogGameRule="dialogGameRule" @dialogClose="handleDialogClose" />
    <!-- obtain synthesis receive-->
    <Mask v-if="showMask" :data="maskData" :visible="showMask" :type="maskType" @click="hideMask"
      @receiveCardSuccess="receiveCardSuccess" />
    <SelectCord :dialogSelectCord="dialogSelectCord" @dialogClose="handleDialogBuyClose" @confirm="handleConfirm" />
    <merge :dialogMerge="dialogMerge" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineAsyncComponent, watch } from 'vue'
import { showToast } from 'vant'
import Empty from '@/components/common/empty.vue'
import { getMyCard, cardList, mergeCard } from '@/api/game.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import GameCard from '@/components/common/GameCard.vue'
import SelectCord from '@/components/game/selectCord.vue'
import Rule from '@/components/game/rule.vue'
import Mask from '@/components/game/mask.vue'
import merge from '@/components//dialog/merge.vue'
import country from '@/components/game/country.vue'
const avatar = defineAsyncComponent(
  () => import('@/components/common/avatar.vue'),
)
import { useRouter } from 'vue-router'
import emitter from '@/utils/mitt'
const router = useRouter()
const dialogGameRule = ref(false)
const showMask = ref(false) // 控制遮罩显示
const maskData = ref({})
const maskType = ref('')
const dialogMerge = ref(false)
const selectedItem = ref(null)
const disabledButton = ref(true)
const selectCord = (item) => {
  console.log(item)
  selectedItem.value = item
  if (item.cardNumber == 2048) {
    maskData.value = item
    maskType.value = 'receive'
    showMask.value = true
  } else {
    getCardList()
  }
}
// 隐藏遮罩
const hideMask = () => {
  showMask.value = false
}
// 我的卡牌
const myCards = ref([])

// 投票项目数据
const voteItems = ref([])
watch(myCards, (val) => {
  if (val.length > 0) {
    disabledButton.value = false
  } else {
    disabledButton.value = true
  }
})
//打开mask2048
const openReceiveMask = (data) => {
  console.log(data)
  maskData.value = data
  maskType.value = 'receive'
  showMask.value = true
}
const handleGameRule = () => {
  dialogGameRule.value = true
}
const handleDialogClose = () => {
  dialogGameRule.value = false
}
const dialogSelectCord = ref(false)
const openSelectCord = (data) => {
  console.log(data)
  maskData.value = data
  // if (myCards.value.length < 2) {
  //   // 直接调用合成接口
  //   handleConfirm(myCards.value[0])
  // } else {
  //   dialogSelectCord.value = true
  // }
  // 直接调用合成接口
  handleConfirm()
}
const handleDialogBuyClose = () => {
  dialogSelectCord.value = false
}
const handleConfirm = () => {
  const params = {
    id: maskData.value.id,
    myCardId: selectedItem.value.id,
  }
  mergeCard(params)
    .then((res) => {
      console.log(res)
      if (res.code == 200) {
        // 合成成功显示mask
        maskData.value.cardColor = res.data.cardColor
        maskData.value.cardNumber = res.data.cardNumber
        showMask.value = true
        maskType.value = 'synthesis'
        //更新我的卡牌
        getMyCardData()
      } else if (res.code == 200010) {
        showToast('自己好友已满')
      } else if (res.code == 200011) {
        showToast('对方好友已满')
      } else if (res.code == 200019) {
        showToast('已添加对方为好友，不可再次申请')
      } else {
        showToast(t('game.synthesisFailed'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('game.synthesisFailed'))
    })
}
// 进入商城
const addShop = () => {
  router.push('/shop')
}
const getMyCardData = () => {
  getMyCard()
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        if (res.data.isFirst) {
          // 第一次进入赠送卡牌
          maskData.value = res.data.card2048List[0]
          maskType.value = 'obtain'
          showMask.value = true
        }
        if (res.data.isContain2048) {
          dialogMerge.value = true
        }
        myCards.value = res.data.card2048List
        selectedItem.value = res.data.card2048List[0]
        getCardList()
      } else {
      }
    })
    .catch(function (error) {
      console.log(error)
    })
}
const getCardList = () => {
  const query = {
    cardId: selectedItem.value ? selectedItem.value.cardId : '',
    cardNumber: selectedItem.value ? selectedItem.value.cardNumber : '',
  }
  cardList(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        voteItems.value = res.rows
      } else {
      }
    })
    .catch(function (error) {
      console.log(error)
    })
}
// 领取传送卡
const receiveCardSuccess = () => {
  getMyCardData()
}
emitter.on('buyCardsSuccess', () => {
  getMyCardData()
})
onMounted(() => {
  // 获取我的卡牌
  getMyCardData()
})
onUnmounted(() => {
  emitter.off('buyCardsSuccess')
})
</script>

<style scoped lang="scss">
.game-container {
  display: flex;
  flex-direction: column;
  background-color: #f0f4f9;
  min-height: 100vh;
  padding: 10px;
}

// 我的卡牌区域样式
.my-cards {
  height: 128px;
  display: flex;
  align-items: center;

  .title {
    font-size: 26px;
    color: rgba(51, 51, 51, 0.8);
  }

  .cards-row {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    overflow: auto;

    .cards-row-item {
      position: relative;
    }

    .selected-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('@/assets/images/game/select.webp') no-repeat center center;
      background-size: 100% 100%;
      z-index: 10;
    }
  }

  .card-action {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;

    img {
      width: 52px;
      height: 52px;
      object-fit: contain;
      margin-left: 28px;
    }
  }
}

// 投票项目列表样式
.vote-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 15px;
  overflow: auto;
}

.vote-item {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  position: relative;

  .user-info {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar-wrapper {
      width: 80px;
      height: 80px;
      border-radius: 4px;
      border: 1px dashed #ccc;
      overflow: hidden;
      margin-right: 14px;
    }

    .avatar {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .user-details {
      flex: 1;
      display: flex;
      flex-direction: column;

      .user-name {
        font-size: 26px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333333;
        display: flex;
        align-items: center;

        img {
          width: 36px;
          height: 36px;
          margin-left: 10px;
        }
      }

      .user-meta {
        display: flex;
        align-items: center;
      }

      .birth-date {
        display: flex;
        align-items: center;
        font-size: 22px;
        color: #999;

        img {
          width: 28px;
          height: 28px;
          margin-right: 5px;
        }
      }
    }
  }

  .action-button {
    display: flex;
    justify-content: center;
    margin-left: 40px;

    .combine-btn {
      background-color: #f7cf27;
      color: #333;
      padding: 6px 44px;
      height: 58px;
      line-height: 58px;
      font-size: 26px;
      text-align: center;
      border-radius: 8px;
    }

    :deep(.van-button--default) {
      border-color: #f7cf27;
      // height: 58px;
      // line-height: 58px;
    }
  }
}

.no-data {
  height: 100%;
}

.affix {
  position: fixed;
  bottom: 25px;
  right: 42px;
  // bottom: 30px;
  // right: 60px;
  z-index: 9999;

  img {
    width: 129px;
    height: 129px;
  }
}
</style>
