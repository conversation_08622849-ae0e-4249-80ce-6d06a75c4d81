<template>
  <div>
    <div class="con">
      <div class="top">
        <img @click="back" src="@/assets/images/index/back (2).png" />
        <div class="title">{{ title }}</div>
      </div>
      <div class="centerCon">
        <div class="item">
          <div class="title">
            <div>{{ $t('report.detailTitle') }}</div>
          </div>
          <div class="itemCon">
            <van-field
              v-model="content"
              rows="6"
              type="textarea"
              maxlength="4000"
              show-word-limit
              :placeholder="t('report.placeholder')"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">
            <div>{{ $t('report.imgTitle') }}</div>
          </div>
          <div class="itemCon">
            <g-upload
              :mode="imgList"
              @chooseFile="chooseFile"
              @imgDelete="imgDelete"
              :control="control"
              :maxCount="maxCount"
              type="report"
            ></g-upload>
          </div>
        </div>
        <div class="publish" @click="publish">
          <div class="btn">{{ $t('report.confirmReport') }}</div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="dialogVisible"
      :showConfirmButton="false"
      :close-on-click-overlay="false"
    >
      <div class="report">
        <img src="@/assets/images/index/succes.png" />
        <div class="title">
          {{ $t('report.reportSuccess') }}
        </div>
        <div class="info">{{ $t('report.reportSuccessInfo') }}</div>
        <div class="publish">
          <div class="btn" @click.stop="publishReport">
            {{ $t('report.reportPublish') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, reactive, onActivated, onUnmounted } from 'vue'
import { showToast } from 'vant'
import emitter from '@/utils/mitt.js'
import gUpload from '@/components/common/g-upload.vue'
import { useRoute, useRouter } from 'vue-router'
import { report } from '@/api/home.js'
import { reportWorks } from '@/api/match.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const route = useRoute()
const router = useRouter()
const data = ref({})
const content = ref('')
const imgList = ref([])
const control = ref(true)
const maxCount = ref(9)
const dialogVisible = ref(false)
const imageList = ref([])
const title = ref('')
onMounted(() => {
  // 防止外部滚动事件影响
  document.body.style.overflow = 'auto'

  // 如果是从视频页面来的，确保视频页面的滚动事件不会影响举报页面
  if (route.query.fromPath && route.query.fromPath.includes('/video')) {
    console.log('从视频页面进入举报页面，阻止事件冒泡')

    // 添加事件拦截器
    const preventPropagation = (e) => {
      e.stopPropagation()
    }

    document
      .querySelector('.con')
      ?.addEventListener('wheel', preventPropagation)

    // 组件卸载时移除事件拦截器
    onUnmounted(() => {
      document
        .querySelector('.con')
        ?.removeEventListener('wheel', preventPropagation)
      emitter.off('enterReport')
    })
  }
})
// 激活钩子函数
onActivated(() => {
  console.log('route', route.query)
  data.value = route.query
  //
  if (data.value.reportType == 'dynamic') {
    const str = t('report.reportEndTitleAfterD')
    const newStr = str
      .replace('%name%', data.value.name)
      .replace('%title%', data.value.title)
    title.value = newStr
  } else if (data.value.reportType == 'comment') {
    const str = t('report.reportEndTitleAfterC')
    const newStr = str
      .replace('%name%', data.value.name)
      .replace('%title%', data.value.title)
    title.value = newStr
  } else if (data.value.reportType == 'reply') {
    const str = t('report.reportEndTitleAfterR')
    const newStr = str
      .replace('%name%', data.value.name)
      .replace('%title%', data.value.title)
    title.value = newStr
  } else if (data.value.reportType == 'match') {
    title.value = t('tooltip.report')
  }
})
emitter.on('enterReport', () => {
  console.log('初始化')
  // 初始化
  content.value = ''
})
const onChange = (values) => {
  console.log(values) // 这里的values是当前所有勾选的复选框的name组成的数组
}
// 上传图片改变
const chooseFile = (data) => {
  console.log(data)
  imageList.value.push(data)
}
const imgDelete = (data) => {
  imageList.value.splice(data.index, 1)
  console.log(imageList.value)
  showToast(t('toast.deleteSuccess'))
}
const back = () => {
  // 检查是否有来源页面信息
  if (data.value.fromPath && data.value.fromPath.includes('/video')) {
    // 如果是从视频页面来的，返回上一个举报页面
    if (route.query.fromPath === '/reportTwo') {
      router.replace({
        path: '/reportTwo',
        query: {
          ...route.query,
          fromPath: '/video',
        },
      })
    } else {
      router.replace({
        path: '/reportOne',
        query: {
          reportType: data.value.reportType,
          complainType: data.value.complainType,
          reportId: data.value.reportId,
          name: data.value.name,
          toComplainUserId: data.value.toComplainUserId,
          id: route.query.id,
          type: route.query.type,
          fromPath: '/video',
        },
      })
    }
  } else if (route.query.fromPath === '/reportTwo') {
    router.replace({
      path: '/reportTwo',
      query: route.query,
    })
  } else {
    // 否则正常返回
    router.go(-1)
  }
  emitter.emit('homeBack')
}

// 点击确认举报
const publish = () => {
  if (data.value.reportType == 'match') {
    reportMatch()
  } else {
    reportDynamic()
  }
}
const reportDynamic = () => {
  if (data.value.reportType == 'dynamic') {
    var reportType = '0'
  } else if (data.value.reportType == 'comment') {
    var reportType = '1'
  } else {
    var reportType = '2'
  }
  const newData = {
    complainType: reportType,
    content: content.value,
    menuId: data.value.menuId,
    id: data.value.reportId,
    imageList: imageList.value,
    toComplainUserId: data.value.toComplainUserId, //被举报人的id
  }
  console.log(newData)
  report(newData)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        dialogVisible.value = true
        showToast(t('report.reportSuccess'))
        ta.track('report', { report_count: 1 })
      } else {
        showToast(t('report.reportFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('report.reportFail'))
    })
}
const reportMatch = () => {
  const newData = {
    complainType: data.value.complainType,
    content: content.value,
    menuId: data.value.menuId,
    contestId: data.value.reportId,
    imageList: imageList.value,
    toComplainUserId: data.value.toComplainUserId, //被举报人的id
  }
  console.log(newData)
  reportWorks(newData)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        dialogVisible.value = true
        showToast(t('report.reportSuccess'))
        ta.track('report', { report_count: 1 })
      } else if (res.code == 500) {
        showToast(t('toast.alreadyReport'))
      } else {
        showToast(t('report.reportFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('report.reportFail'))
    })
}
const publishReport = () => {
  dialogVisible.value = false
  content.value = ''
  imageList.value = []
  imgList.value = []

  // 否则根据backNum决定返回层级
  if (data.value.backNum == 2) {
    router.go(-2)
  } else {
    router.go(-3)
  }
  // 发送homeBack事件，确保恢复滚动位置
  emitter.emit('homeBack')
}
onUnmounted(() => {
  emitter.off('enterReport')
})
</script>
<style></style>
<style lang="scss" scoped>
@import '@/assets/css/common/reportCheckbox.scss';

:deep(.van-cell) {
  background: #f3f3f3;
}

:deep(.van-field__control) {
  font-size: var(--size_26);
  line-height: 37px;
}

.con {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .centerCon {
    flex: 1;
    overflow: auto;

    .item {
      .title {
        display: flex;
        height: 46px;
        font-size: var(--size_32);
        color: var(--mainTtileColor);
        margin-bottom: 12px;

        text {
          color: #ff6464;
          margin-left: 9px;
        }
      }

      .itemCon {
        margin-bottom: 30px;
      }
    }

    .publish {
      margin-bottom: 50px;
    }
  }
}

:deep(.van-popup) {
  width: 622px;
  border-radius: 8px;
}

.report {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 66px;

  img {
    width: 118px;
    height: 118px;
    margin: 48px 0 34px;
  }

  .title {
    font-weight: 500;
    font-size: var(--size_32);
    color: var(--mainTtileColor);
    line-height: 48px;
  }

  .info {
    color: var(--bearTtileColor);
    font-size: var(--size_28);
    line-height: 40px;
    margin: 8px 0 112px;
    text-align: center;
  }

  .publish {
    display: flex;
    align-items: center;
    justify-content: center;

    .btn {
      margin-bottom: 52px;
      // width: 282px;
      padding: 0 50px;
      height: 68px;
      display: flex;
      align-items: center;
      text-align: center;
      color: #c4e5ff;
      font-size: var(--size_28);
      font-weight: var(--weight5);
      background: url('@/assets/images/index/publish.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
