<template>
  <div v-click-outside="handleClickOutside">
    <van-popover
      v-model:show="showPopover"
      theme="dark"
      placement="bottom-end"
      :actions="list"
      @select="onSelect"
      teleport="body"
      :close-on-click-outside="true"
    >
      <template #reference>
        <div v-if="isShowBtn">
          <img
            v-if="imgType == 'dynamic'"
            @click="onClick"
            class="dynamicImg"
            src="@/assets/images/index/more.png"
          />
          <img
            v-else-if="imgType == 'videoPlay'"
            class="videoPlayImg"
            @click="onClick"
            src="@/assets/images/video/more.webp"
          />
          <img
            v-else
            class="detailImg"
            @click="onClick"
            src="@/assets/images/detail/more.png"
          />
        </div>
      </template>
    </van-popover>
    <deleteMsg
      v-if="dialogDelete"
      :dialogDelete="dialogDelete"
      @dialogClose="dialogClose"
      @confirm="confirmDelete"
    >
    </deleteMsg>
    <block
      v-if="dialogBlock"
      :dialogBlock="dialogBlock"
      @dialogClose="dialogCloseBlock"
      @confirm="confirmBlock"
    >
    </block>
  </div>
</template>
<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { showToast } from 'vant'
import { useRouter } from 'vue-router'
import emitter from '@/utils/mitt.js'
const deleteMsg = defineAsyncComponent(
  () => import('@/components/dialog/deleteMsg.vue'),
)
const block = defineAsyncComponent(
  () => import('@/components/dialog/block.vue'),
)
import {
  deleteDynamic,
  commentDelete,
  collect,
  replyDelete,
  getBlock,
} from '@/api/home.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const router = useRouter()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  tooltipType: {
    type: String,
    default: '',
  },
  commentId: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
  imgType: {
    type: String,
    default: '',
  },
})
const showPopover = ref(false)
const list = ref([])
const dialogDelete = ref(false)
const dialogBlock = ref(false)
const deleteType = ref('')
const reportType = ref('')
const emits = defineEmits(['closeDetail', 'changeReply'])
const isShowBtn = ref(true)

// 处理点击外部事件
const handleClickOutside = (isOutside, event) => {
  // 当点击外部区域且popover正在显示时，关闭popover
  if (isOutside && showPopover.value) {
    showPopover.value = false
  }
}

onMounted(() => {
  if (props.type == 'msg') {
    //判断当前动态详情是否是自己
    console.log(props.data.userId == countStore.loginData.roleId)
    if (props.data.userId == countStore.loginData.roleId) {
      // 是自己
      list.value = [
        {
          value: 0,
          text: t('tooltip.delete'),
        },
      ]
    } else {
      list.value = [
        {
          value: 0,
          text: t('tooltip.report'),
        },
        {
          value: 1,
          text: t('tooltip.block'),
        },
      ]
    }
  } else {
    if (props.tooltipType == 'otherDynamic' || props.tooltipType == 'myLike') {
      if (props.data.userId == countStore.loginData.roleId) {
        isShowBtn.value = false
      } else {
        list.value = [
          {
            value: 0,
            text: t('tooltip.report'),
          },
          {
            value: 1,
            text: t('tooltip.block'),
          },
        ]
      }
    } else if (props.tooltipType == 'myDynamic') {
      list.value = [
        {
          value: 0,
          text: t('tooltip.delete'),
        },
      ]
      // , {
      //     value: 1,
      //     text: t('tooltip.modify')
      // }
    } else if (props.tooltipType == 'myCollect') {
      if (props.data.userId == countStore.loginData.roleId) {
        list.value = [
          {
            value: 0,
            text: t('tooltip.cancelCollect'),
          },
        ]
      } else {
        list.value = [
          {
            value: 0,
            text: t('tooltip.cancelCollect'),
          },
          {
            value: 1,
            text: t('tooltip.report'),
          },
          {
            value: 1,
            text: t('tooltip.block'),
          },
        ]
      }
    } else if (
      props.tooltipType == 'commentReport' ||
      props.tooltipType == 'replyReport'
    ) {
      // 先判断是否是自己发的
      if (props.data.userId == countStore.loginData.roleId) {
        list.value = [
          {
            value: 1,
            text: t('tooltip.delete'),
          },
        ]
      } else {
        list.value = [
          {
            value: 0,
            text: t('tooltip.report'),
          },
          {
            value: 1,
            text: t('tooltip.block'),
          },
        ]
      }
    } else if (props.tooltipType == 'myFriend') {
      // 自己的 删除修改
      // 其他人的 举报 判断是否取消收藏
      if (props.data.userId == countStore.loginData.roleId) {
        // 是自己
        list.value = [
          {
            value: 0,
            text: t('tooltip.delete'),
          },
        ]
        // , {
        //     value: 1,
        //     text: t('tooltip.modify')
        // }
      } else {
        list.value = [
          {
            value: 0,
            text: t('tooltip.report'),
          },
          {
            value: 1,
            text: t('tooltip.block'),
          },
        ]
      }
    }
  }

  console.log(props.tooltipType)
  // console.log(list.value)
})

const dialogCloseBlock = () => {
  dialogBlock.value = false
}
const confirmBlock = () => {
  console.log('确认屏蔽')
  console.log(props.commentId)
  getBlock(props.data.userId)
    .then((res) => {
      console.log(res)
      if (res.data == 200) {
        // 通知index更新列表
        if (
          props.tooltipType == 'commentReport' ||
          props.tooltipType == 'replyReport'
        ) {
          emitter.emit('refurbishComment', props.data.dynamicsId)
        } else {
          emits('closeDetail')
          emitter.emit('blockDySuccess')
        }
        showToast(t('toast.blockSuccess'))
      } else if (res.data == 200048) {
        showToast(t('toast.blockListFull'))
      } else {
        showToast(t('toast.blockFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.blockFail'))
    })
}
// 删除确认框关闭
const dialogClose = () => {
  dialogDelete.value = false
}
// 确认删除
const confirmDelete = () => {
  console.log('确认删除')
  console.log(props.data)
  console.log(deleteType.value)
  if (deleteType.value == 'dynamic') {
    deleteDynamic(props.data.id)
      .then((res) => {
        console.log(res)
        if (res.code == 200) {
          // 通知index更新列表
          emitter.emit('deleteDySuccess', props.data.id)
          emits('closeDetail')
          showToast(t('toast.deleteSuccess'))
        } else {
          showToast(t('toast.deleteFail'))
        }
      })
      .catch(function (error) {
        console.log(error)
        showToast(t('toast.deleteFail'))
      })
  } else if (deleteType.value == 'comment') {
    commentDelete(props.data.id)
      .then((res) => {
        console.log(res)
        if (res.code === 200) {
          // 通知comment更新列表
          emitter.emit('deleteCommentList', props.data.dynamicsId)
          showToast(t('toast.deleteSuccess'))
        } else {
          showToast(t('toast.deleteFail'))
        }
      })
      .catch(function (error) {
        console.log(error)
        showToast(t('toast.deleteFail'))
      })
  } else {
    replyDelete(props.data.id)
      .then((res) => {
        console.log(res)
        if (res.code === 200) {
          // emitter.emit('deleteCommentList', props.data.dynamicsId)
          // 通知comment更新列表
          // emitter.emit('deleteRelpyList', props.commentId)
          emits('changeReply', props.data.id)
          showToast(t('toast.deleteSuccess'))
        } else {
          showToast(t('toast.deleteFail'))
        }
      })
      .catch(function (error) {
        console.log(error)
        showToast(t('toast.deleteFail'))
      })
  }
}
// 取消收藏
const cancelCollect = () => {
  const query = {
    dyNamicsId: props.data.id,
  }
  collect(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        // 通知index更新列表  取消收藏
        emitter.emit('cancelCollects')
        showToast(t('toast.collectCancelSuccess'))
        ta.track('collectCancel', { collect_count: 1 })
      } else {
        showToast(t('toast.collectCancelFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.collectCancelFail'))
    })
}
const onClick = (event) => {
  // 阻止事件冒泡，防止点击事件传播到外层元素
  event.stopPropagation()
  showPopover.value = true
}
// 点击
const onSelect = (data) => {
  console.log(data)
  console.log(props.data)
  console.log(props.type)
  if (props.type == 'msg' || props.tooltipType == 'myFriend') {
    //判断当前动态详情是否是自己
    if (props.data.userId == countStore.loginData.roleId) {
      if (data.value == 0) {
        // 删除
        console.log('删除')
        dialogDelete.value = true
        deleteType.value = 'dynamic'
      } else {
        // 修改
        // console.log('修改')
        // console.log(props.data)
        // emitter.emit('modifyAdd', props.data)
      }
    } else {
      if (data.value == 0) {
        // 进入举报页面
        if (props.data.isComplain) {
          showToast(t('toast.alreadyReport'))
        } else {
          reportType.value = 'dynamic'
          const reportData = {
            reportType: reportType.value,
            reportId: props.data.id,
            name: props.data.userName,
            toComplainUserId: props.data.userId,
          }
          navigateToReport(reportType.value, reportData)
        }
      } else {
        //屏蔽
        dialogBlock.value = true
      }
    }
  } else {
    if (props.tooltipType == 'otherDynamic') {
      if (data.value == 0) {
        // 进入举报页面
        if (props.data.isComplain) {
          showToast(t('toast.alreadyReport'))
        } else {
          reportType.value = 'dynamic'
          const reportData = {
            reportType: reportType.value,
            reportId: props.data.id,
            name: props.data.userName,
            toComplainUserId: props.data.userId,
          }
          navigateToReport(reportType.value, reportData)
        }
      } else {
        //屏蔽
        dialogBlock.value = true
      }
    } else if (props.tooltipType == 'myDynamic') {
      if (data.value == 0) {
        // 删除
        console.log('删除')
        dialogDelete.value = true
        deleteType.value = 'dynamic'
      } else {
        // 修改
        // console.log('修改')
        // console.log(props.data)
        // emitter.emit('modifyAdd', props.data)
      }
    } else if (props.tooltipType == 'myCollect') {
      if (data.value == 0) {
        // 取消收藏
        console.log('取消收藏')
        cancelCollect()
      } else if (data.value == 1) {
        // 进入举报页面
        if (props.data.isComplain) {
          showToast(t('toast.alreadyReport'))
        } else {
          reportType.value = 'dynamic'
          const reportData = {
            reportType: reportType.value,
            reportId: props.data.id,
            name: props.data.userName,
            toComplainUserId: props.data.userId,
          }
          navigateToReport(reportType.value, reportData)
        }
      } else {
        //屏蔽
        dialogBlock.value = true
      }
    } else if (props.tooltipType == 'myLike') {
      if (data.value == 0) {
        // 进入举报页面
        if (props.data.isComplain) {
          showToast(t('toast.alreadyReport'))
        } else {
          reportType.value = 'dynamic'
          const reportData = {
            reportType: reportType.value,
            reportId: props.data.id,
            name: props.data.userName,
            toComplainUserId: props.data.userId,
          }
          navigateToReport(reportType.value, reportData)
        }
      } else {
        //屏蔽
        dialogBlock.value = true
      }
    } else if (
      props.tooltipType == 'commentReport' ||
      props.tooltipType == 'replyReport'
    ) {
      // 先判断是否是自己发的
      if (props.data.userId == countStore.loginData.roleId) {
        // 删除
        console.log('删除')
        dialogDelete.value = true
        props.tooltipType == 'commentReport'
          ? (deleteType.value = 'comment')
          : (deleteType.value = 'reply')
      } else {
        if (data.value == 0) {
          // 进入举报页面
          props.tooltipType == 'commentReport'
            ? (reportType.value = 'comment')
            : (reportType.value = 'reply')
          if (props.data.isComplain) {
            showToast(t('toast.alreadyReport'))
          } else {
            const reportData = {
              reportType: reportType.value,
              reportId: props.data.id,
              name: props.data.userName,
              toComplainUserId: props.data.userId,
            }
            navigateToReport(reportType.value, reportData)
          }
        } else {
          //屏蔽
          dialogBlock.value = true
        }
      }
    }
  }
}
// 进入举报页面的通用函数
const navigateToReport = (reportType, reportData) => {
  router.push({ path: '/reportOne', query: reportData })
  emitter.emit('enterReport')
}
</script>

<style lang="scss" scoped>
:deep(.van-popover__action) {
  width: 100%;
  font-size: 26px;
  color: #e1e1e1;
  // padding: 20px 30px;
}

.dynamicImg {
  width: 38px;
  height: 38px;
}

.detailImg {
  width: 28px;
  height: 28px;
  // padding-left: 22px;
}

.videoPlayImg {
  width: 50px;
  height: 50px;
}
</style>
