<template>
  <div>
    <van-popup v-model:show="dialogVisible" title="" show-cancel-button @close="handleClose" @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay">
      <div class="deleteCon">
        <div class="title">{{ $t('game.selectCards') }}</div>
        <div class="con">
          <div class="goods-list">
            <div class="goods-item" v-for="item in data" :key="item.id" @click="selectCord(item)">
              <div class="goods-item-card">
                <GameCard :color="item.cardColor" :number="item.cardNumber" cardType="mergeToast" />
              </div>
              <div class="selected-overlay" v-if="selectedItem === item"></div>
            </div>
          </div>
        </div>
        <div class="buttom">
          <div class="buttomBtn" @click="handleCancel">
            {{ $t('delete.deleteCancel') }}
          </div>
          <div class="buttomBtn activeBtn" @click="handleConfirm">
            {{ $t('delete.deleteConfirm') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import GameCard from '@/components/common/GameCard.vue'
import { getMyCard } from '@/api/game.js'
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const dialogVisible = ref(false)
const selectedItem = ref(null)
const props = defineProps({
  dialogSelectCord: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogSelectCord,
  (val) => {
    // console.log(val)
    dialogVisible.value = val
    if (val == true) {
      getMyCardData()
    }
  },
  { immediate: true },
)

const emits = defineEmits(['dialogClose', 'confirm'])
const data = ref([])
const selectCord = (item) => {
  selectedItem.value = item
}
// 确认按钮
const handleConfirm = () => {
  emits('confirm', selectedItem.value)
  emits('dialogClose')
}
// 取消按钮
const handleCancel = () => {
  emits('dialogClose')
}
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const onClickPopup = () => { }
const onClickOverlay = () => { }
const getMyCardData = () => {
  getMyCard()
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        data.value = res.data.card2048List
        selectedItem.value = res.data.card2048List[0]
      } else {
      }
    })
    .catch(function (error) {
      console.log(error)
    })
}
</script>
<style lang="scss" scoped>
// @import '@/assets/css/common/deleteMsg.scss';
:deep(.van-popup) {
  background: none;
}

.deleteCon {
  width: 730px;
  height: 90vh;
  background: url('@/assets/images/game/selectBg.webp') no-repeat center center;
  background-size: 100% 100%;
  padding: 16px 40px;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 30px;
    color: #224c8b;
    font-weight: bold;
    text-align: center;
    height: 80px;
  }

  .con {
    padding: 14px 16px;
    background: rgba(152, 190, 232, 0.3);
    flex: 1;
    overflow: auto;

    .goods-list {
      margin: 14px 16px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .goods-item {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .goods-item-card {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .selected-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('@/assets/images/game/select.webp') no-repeat center center;
        background-size: 100% 100%;
        z-index: 10;
      }
    }
  }
}

.buttom {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 20px;
  font-size: 30px;
  margin-top: 20px;

  .buttomBtn {
    width: 100%;
    height: 92px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #222222;
    text-align: center;
    background: url('@/assets/images/game/cancel.webp') no-repeat;
    background-size: 100% 100%;
  }

  .activeBtn {
    color: #ffffff;
    background: url('@/assets/images/game/shop-Btn.webp') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
