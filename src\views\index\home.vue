<template>
  <div class="container">
    <Menu type="detail"></Menu>
    <div class="con">
      <User
        :roleId="data.userId"
        type="home"
        :data="userData"
        @uploadData="uploadData"
      ></User>
      <div v-if="loading">
        <skeleton />
      </div>
      <div v-else class="waterFallCon" ref="scrollContainerRef">
        <van-list
          class="vanList"
          ref="vanListRef"
          @load="changeWaterfallData"
          :immediate-check="true"
          :offset="1"
        >
          <div v-if="list.length">
            <div
              v-masonry="'home-masonry'"
              transition-duration="0"
              item-selector=".item"
            >
              <div
                v-masonry-tile
                class="item"
                v-for="(item, index) in list"
                :key="index"
              >
                <Waterfall
                  :dynamicType="dynamicType"
                  :type="type"
                  :data="item"
                  :index="index"
                  class="water-fall"
                  @showDetail="showDetail"
                />
              </div>
            </div>
          </div>
          <div v-else class="noComment">
            <Empty :title="$t('empty.content')" />
          </div>
        </van-list>
      </div>
    </div>
    <div v-if="isShowDetail">
      <Detail
        :data="detailData"
        :tooltipType="tooltipType"
        :isShowDetail="isShowDetail"
        :index="index"
        @closePopup="closePopup"
      ></Detail>
    </div>
    <videoList
      v-if="dialogVideo"
      :dialogVideo="dialogVideo"
      :data="dialogVideoData"
      @dialogClose="dialogCloseVideo"
    >
    </videoList>
  </div>
</template>
<script setup>
import Menu from '@/components/menu.vue'
import Waterfall from '@/components/index/waterFall.vue'
import User from '@/components/index/user.vue'
import {
  ref,
  onMounted,
  onUnmounted,
  nextTick,
  inject,
  defineAsyncComponent,
  onActivated,
  onDeactivated,
  watch,
} from 'vue'
import { showToast } from 'vant'
const Detail = defineAsyncComponent(
  () => import('@/components/detail/index.vue'),
)
const skeleton = defineAsyncComponent(
  () => import('@/components/common/skeleton.vue'),
)
const Empty = defineAsyncComponent(
  () => import('@/components/common/empty.vue'),
)
const videoList = defineAsyncComponent(
  () => import('@/components/dialog/videoList.vue'),
)
const $redrawVueMasonry = inject('redrawVueMasonry')
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import {
  user,
  getDynamicList,
  friendData,
  getAvatarld,
  getDynamicDetail,
} from '@/api/home.js'
import _ from 'lodash'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const loading = ref(false)
const data = ref({})
const list = ref([])
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const userData = ref({})
const dynamicType = ref('')
const type = 'dynamic'
const avatarId = ref('')
const detailData = ref({})
const isShowDetail = ref(false)
const tooltipType = ref('')
const index = ref()
const scrollPosition = ref(0)
const throttledSaveScroll = ref(null) // 存储节流函数引用
const vanListRef = ref(null) // 引用滚动容器
const scrollContainerRef = ref(null) // 新增滚动容器引用
const imagesPreloaded = ref(false) // 跟踪图片是否已预加载
const dialogVideo = ref(false)
const dialogVideoData = ref({})
const dialogCloseVideo = () => {
  dialogVideo.value = false
}
// 获取正确的滚动容器
const getScrollContainer = () => {
  // 首先尝试使用ref引用
  if (scrollContainerRef.value) {
    return scrollContainerRef.value
  }

  // 如果找不到scrollContainerRef，尝试使用vanListRef
  if (vanListRef.value) {
    const rootEl = vanListRef.value.$el
    return rootEl
  }

  // 最后尝试直接获取DOM元素
  return (
    document.querySelector('.waterFallCon') ||
    document.querySelector('.vanList')
  )
}

// 强制保存当前滚动位置
const forceSaveScrollPosition = () => {
  const scrollContainer = getScrollContainer()

  if (scrollContainer) {
    const currentScrollTop = scrollContainer.scrollTop

    // 只有当滚动位置大于0时才保存
    if (currentScrollTop > 0) {
      scrollPosition.value = currentScrollTop

      // 保存到localStorage
      try {
        localStorage.setItem('homeScrollPosition', currentScrollTop.toString())
      } catch (e) {
        console.error('保存滚动位置到localStorage失败:', e)
      }
    }
  }

  // 返回当前滚动位置，无论是否成功保存
  return scrollContainer ? scrollContainer.scrollTop : 0
}

// 添加滚动监听
const addScrollListener = () => {
  // 先移除可能存在的旧监听器
  removeScrollListener()

  const scrollContainer = getScrollContainer()

  if (scrollContainer) {
    throttledSaveScroll.value = _.throttle(() => {
      const currentPos = scrollContainer.scrollTop

      // 只有当滚动位置大于0时才保存
      if (currentPos > 0) {
        scrollPosition.value = currentPos

        // 保存到localStorage
        try {
          localStorage.setItem('homeScrollPosition', currentPos.toString())
        } catch (e) {
          console.error('保存滚动位置到localStorage失败:', e)
        }
      }
    }, 50) // 降低节流时间，提高响应速度

    scrollContainer.addEventListener('scroll', throttledSaveScroll.value)

    // 初始保存一次当前位置
    setTimeout(forceSaveScrollPosition, 50)

    return true
  } else {
    // 如果第一次尝试失败，再次尝试
    setTimeout(() => {
      const retryContainer = getScrollContainer()
      if (retryContainer) {
        throttledSaveScroll.value = _.throttle(() => {
          const currentPos = retryContainer.scrollTop

          // 只有当滚动位置大于0时才保存
          if (currentPos > 0) {
            scrollPosition.value = currentPos

            // 保存到localStorage
            try {
              localStorage.setItem('homeScrollPosition', currentPos.toString())
            } catch (e) {
              console.error('保存滚动位置到localStorage失败:', e)
            }
          }
        }, 50)

        retryContainer.addEventListener('scroll', throttledSaveScroll.value)

        // 初始保存一次当前位置
        setTimeout(forceSaveScrollPosition, 50)

        return true
      }

      return false
    }, 100)
  }

  return false
}

// 移除滚动监听
const removeScrollListener = () => {
  if (throttledSaveScroll.value) {
    const scrollContainer = getScrollContainer()
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', throttledSaveScroll.value)
      console.log('已移除home页面滚动事件监听')
    }
  }
}

// 离开页面时保存状态
onDeactivated(() => {
  // 移除滚动监听
  removeScrollListener()
})

// 恢复滚动位置
const restoreScrollPosition = () => {
  // 使用更高效的方法恢复滚动位置
  const scrollContainer = getScrollContainer()

  if (!scrollContainer) {
    console.error('未找到滚动容器，无法恢复滚动位置')
    // 延迟一次重试
    setTimeout(() => {
      const retryContainer = getScrollContainer()
      if (retryContainer && scrollPosition.value > 0) {
        console.log('延迟恢复滚动位置:', scrollPosition.value)
        retryContainer.scrollTop = scrollPosition.value
      }
    }, 100)
    return
  }

  // 直接设置滚动位置，不使用多次尝试的方式
  if (scrollPosition.value > 0) {
    console.log('立即恢复滚动位置:', scrollPosition.value)
    // 使用requestAnimationFrame确保在下一帧渲染前设置滚动位置
    requestAnimationFrame(() => {
      scrollContainer.scrollTop = scrollPosition.value

      // 如果首次设置不成功，再尝试一次
      if (Math.abs(scrollContainer.scrollTop - scrollPosition.value) > 10) {
        requestAnimationFrame(() => {
          scrollContainer.scrollTop = scrollPosition.value
        })
      }
    })
  }
}

onActivated(() => {
  console.log('激活home')
  console.log('route', route.query)
  console.log('data', data.value)

  // 检查localStorage中是否有保存的滚动位置
  try {
    const savedPosition = localStorage.getItem('homeScrollPosition')
    if (savedPosition && !isNaN(Number(savedPosition))) {
      scrollPosition.value = Number(savedPosition)
      console.log('从localStorage获取home页面滚动位置:', scrollPosition.value)
    }
  } catch (e) {
    console.error('从localStorage获取滚动位置失败:', e)
  }

  if (route.query.userId == data.value.userId) {
    // 是同一个用户不做处理
    nextTick(() => {
      // 强制重绘瀑布流
      $redrawVueMasonry('home-masonry')
      console.log('home页面瀑布流重绘完成')

      // 优先恢复滚动位置，然后再添加监听
      if (scrollPosition.value > 0) {
        // 立即尝试恢复滚动位置
        restoreScrollPosition()
      }

      // 添加滚动监听
      setTimeout(() => {
        addScrollListener()
      }, 100)
    })
  } else {
    // 不是同一个用户，更新data
    updateData()
    // 重置滚动位置
    scrollPosition.value = 0
    try {
      localStorage.removeItem('homeScrollPosition')
    } catch (e) {
      console.error('清除localStorage滚动位置失败:', e)
    }
  }
})

onMounted(() => {
  console.log('首次进入home')
  updateData()

  // 添加滚动监听
  setTimeout(() => {
    addScrollListener()
  }, 500)

  // 添加DOM变化观察器
  setTimeout(() => {
    const container = document.querySelector('.waterFallCon')
    if (container) {
      const observer = new MutationObserver(
        _.debounce(() => {
          console.log('DOM结构发生变化，重新添加滚动监听')
          checkAndResetScrollListener()
        }, 500),
      )

      observer.observe(container, {
        childList: true,
        subtree: true,
      })
    }
  }, 1000)
})

// 更新数据
const updateData = () => {
  userData.value = {}
  console.log(userData.value)
  console.log('route', route.query)
  data.value = route.query

  // 检查是否是从视频页面进入
  const fromPath = route.query.fromPath
  if (fromPath && fromPath.includes('/video')) {
    console.log('从视频页面进入home页面')
  }

  if (data.value.userId == countStore.loginData.roleId) {
    dynamicType.value = 'oneself'
    userInfo()
  } else {
    dynamicType.value = 'other'
    getFriendData()
  }
  pageNum.value = 1
  list.value = []
  loading.value = true
  nextTick(() => {
    $redrawVueMasonry()
  })
}
const showDetail = (datas, type, isShow, indexs) => {
  if (datas.videoFrame) {
    getVideoDetails(datas.id)
  } else {
    detailData.value = datas
    tooltipType.value = type
    isShowDetail.value = isShow
    index.value = indexs
  }
}
// 获取视频详情
const getVideoDetails = async (videoId) => {
  try {
    const res = await getDynamicDetail(videoId)
    if (res.code === 200) {
      console.log(res)
      const data = {
        id: videoId,
        type: 1,
        fromPath: '/home',
        userId: res.data.userId,
      }
      // router.push({ path: '/video', query: data })
      dialogVideoData.value = data
      dialogVideo.value = true
    } else if (res.code == 500) {
      showToast(t('toast.dynamicFail'))
      //刷新列表
      pageNum.value = 1
      loading.value = true
      list.value = []
      getList(1)
    }
  } catch (error) {
    console.error('获取视频详情失败:', error)
  }
}
const closePopup = () => {
  isShowDetail.value = false
}
const getFriendData = () => {
  const query = {
    roleId: data.value.userId,
  }
  friendData(query)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code === 200) {
        userInfo()
      } else {
        showToast(t('toast.loginFail'))
      }
    })
    .catch((err) => {
      // 接口调用失败之后的操作
      console.log(err)
      showToast(t('toast.loginFail'))
    })
}
const userInfo = () => {
  const userType = dynamicType.value == 'oneself' ? 1 : 2
  const userId = data.value.userId
  user(userType, userId)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        userData.value = res.data
        getAvatarldData(res.data.roleId)
        getList(1)
      } else if (res.code === 401) {
      } else {
        showToast(t('toast.getMsgFail'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.getMsgFail'))
    })
}
// 更新数据
const changeWaterfallData = _.debounce(
  (params) => {
    if (list.value.length < total.value) {
      pageNum.value++
      getList(1)
      console.log('更新数据list')
      console.log(pageNum.value)
    }
  },
  1000,
  { leading: true, trailing: false },
)
const getList = (type) => {
  const query = {
    dynamicsType: type,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    userId: data.value.userId,
  }
  getDynamicList(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        total.value = res.total
        if (res.rows) {
          if (pageNum.value == 1) {
            list.value = res.rows
            nextTick(() => {
              $redrawVueMasonry()
              // 在列表重绘后重新添加滚动监听
              setTimeout(() => {
                addScrollListener()
              }, 300)
            })
          } else {
            res.rows.forEach((item) => {
              list.value.push(item)
            })
            // 在列表更新后重新添加滚动监听
            nextTick(() => {
              try {
                const rootEl = vanListRef.value.$el
                // 计算新增内容后，保持相对位置
                $redrawVueMasonry()
                // 重新添加滚动监听
                setTimeout(() => {
                  addScrollListener()
                }, 100)
              } catch (err) {
                console.error('维持滚动位置失败:', err)
              }
            })
          }
        }
        console.log(list.value)
        loading.value = false
      }
    })
    .catch(function (error) {
      console.log(error)
      loading.value = false
    })
}
const getAvatarldData = (id) => {
  getAvatarld(id)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code === 200) {
        avatarId.value = res.data.avatarId
        emitter.emit('newAvatarId', avatarId.value)
      } else {
        showToast(t('toast.getMsgFail'))
      }
    })
    .catch((err) => {
      // 接口调用失败之后的操作
      console.log(err)
      showToast(t('toast.getMsgFail'))
    })
}
const uploadData = (data) => {
  userData.value = data
}
// 监听图片加载完成事件，触发瀑布流重绘
emitter.on('imageLoaded', (itemId) => {
  console.log('home页面图片加载完成，触发瀑布流重绘:', itemId)

  // 使用防抖，避免频繁重绘
  if (window.homeMasonryRedrawTimer) {
    clearTimeout(window.homeMasonryRedrawTimer)
  }

  window.homeMasonryRedrawTimer = setTimeout(() => {
    console.log('home页面瀑布流重绘')
    $redrawVueMasonry('home-masonry')
  }, 100) // 100ms防抖
})

// 监听到删除动态
emitter.on('deleteDySuccess', (data) => {
  // 判断是动态还是消息更新对应的列表
  console.log(data)
  pageNum.value = 1
  list.value = []
  getList(1)
  // 重新添加滚动监听
  setTimeout(() => {
    addScrollListener()
  }, 500)
})
// 监听到tooltip取消收藏
emitter.on('cancelCollects', () => {
  pageNum.value = 1
  list.value = []
  getList(1)
  // 重新添加滚动监听
  setTimeout(() => {
    addScrollListener()
  }, 500)
})
//监听到强制刷新
emitter.on('refurbishList', () => {
  pageNum.value = 1
  list.value = []
  getList(1)
  // 重新添加滚动监听
  setTimeout(() => {
    addScrollListener()
  }, 500)
})

// 添加一个函数来检查和重置滚动监听
const checkAndResetScrollListener = () => {
  console.log('检查并重置滚动监听')
  // 先移除可能存在的滚动监听
  removeScrollListener()
  // 然后重新添加
  setTimeout(() => {
    addScrollListener()
  }, 300)
}

onUnmounted(() => {
  // 移除滚动事件监听
  removeScrollListener()

  // 清除防抖定时器
  if (window.homeMasonryRedrawTimer) {
    clearTimeout(window.homeMasonryRedrawTimer)
  }

  emitter.off('imageLoaded')
  emitter.off('deleteDySuccess')
  emitter.off('cancelCollects')
  emitter.off('refurbishList')
})
</script>
<style scoped lang="scss">
.container {
  display: flex;

  .menuSh {
    width: 18px;
    background: url('@/assets/images/index/leftMenuBgShow.webp') no-repeat;
    // background-size: 100% 100%;
  }

  .con {
    height: 100vh;
    flex: 1;
    overflow: hidden;
    background: var(--bgColor);
    display: flex;
    flex-direction: column;

    .header {
      padding: 10px 15px;

      .back-btn {
        display: flex;
        align-items: center;
        font-size: 28px;
        color: var(--mainTtileColor);

        img {
          width: 40px;
          height: 40px;
          margin-right: 8px;
        }
      }
    }

    .waterFallCon {
      flex: 1;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      /* 增强iOS滚动体验 */
      will-change: scroll-position;
      /* 优化滚动性能 */
      overscroll-behavior: contain;
      /* 防止过度滚动 */

      .vanList {
        height: 100%;
        transform: translateZ(0);
        /* 启用GPU加速 */

        .item {
          width: 48%;
          position: relative;
          margin-bottom: 18px;
          min-height: 80px;
          will-change: transform;
          /* 优化变换性能 */
        }

        .noComment {
          height: 81vh;
        }
      }
    }
  }
}
</style>
