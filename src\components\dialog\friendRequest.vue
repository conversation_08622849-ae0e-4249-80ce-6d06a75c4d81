<template>
  <div>
    <van-popup v-model:show="dialogVisible" title="" show-cancel-button @close="handleClose" @click.stop="onClickPopup"
      @click-overlay.stop="onClickOverlay" :close-on-click-overlay="false">
      <div class="deleteCon">
        <div class="top">
          <div class="title">{{ $t('index.user.friendRequest') }}</div>
          <img @click="handleCancel" src="@/assets/images/friend/close.webp" />
        </div>
        <div class="bottomCon">
          <div class="material">
            <avatar :url="data.icon" className="friendRequest"></avatar>
            <div class="info">
              <div class="name">{{ data.name }}</div>
              <img src="@/assets/images/friend/nv.webp" />
            </div>
          </div>
          <div class="validation">
            {{ $t('index.user.verificationMessage') }}
          </div>
          <!-- <van-field v-model="textarea" rows="5" type="textarea" maxlength="2000" show-word-limit
                            :placeholder="$t('add.input')" /> -->
          <van-field v-model="input" type="textarea" maxlength="15"
            :placeholder="$t('index.user.verificationMessagePlaceholder')" show-word-limit />
          <div class="buttomBtn" @click="handleConfirm">
            {{ $t('index.user.sendNow') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onMounted, watch, defineAsyncComponent } from 'vue'
import { showToast } from 'vant'
import { applyFriend } from '@/api/translation.js'
const avatar = defineAsyncComponent(
  () => import('@/components/common/avatar.vue'),
)
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const dialogVisible = ref(false)
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const input = ref('')
const props = defineProps({
  dialogFriendRequest: {
    type: Boolean,
    require: false,
    default: false,
  },
  data: {
    type: Object,
    default: {},
  },
  avatarId: {
    type: String,
    default: '',
  },
})
watch(
  () => props.dialogFriendRequest,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClose', 'confirm'])
// 确认按钮
const handleConfirm = () => {
  const query = {
    avatarId: countStore.loginData.avatarId,
    roleId: countStore.loginData.roleId,
    friendAvatarId: props.avatarId,
    friendRoleId: props.data.roleId,
    msg: input.value,
  }
  console.log(query)
  applyFriend(query)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      emits('dialogClose')
      if (res.code === 0) {
        emits('confirm')
        showToast(t('toast.applySuccess'))
      } else if (res.code == 200043) {
        showToast(t('toast.blacklistPrompt'))
      } else if (res.code == 200011) {
        showToast(t('toast.friendNumPrompt'))
      } else if (res.code == 200010) {
        showToast(t('toast.myNumPrompt'))
      } else if (res.code == 200018) {
        showToast(t('toast.failedPrompt'))
      } else if (res.code == 200019) {
        showToast(t('toast.alignPrompt'))
      } else if (res.code == 200015) {
        showToast(t('toast.applyMyPrompt'))
      } else if (res.code == 200016) {
        showToast(t('toast.alignApply'))
      } else {
        showToast(t('toast.applyFail'))
      }
    })
    .catch((err) => {
      // 接口调用失败之后的操作
      console.log(err)
      emits('dialogClose')
      showToast(t('toast.applyFail'))
    })
}
// 取消按钮
const handleCancel = () => {
  emits('dialogClose')
}
// 关闭dialog
const handleClose = () => {
  emits('dialogClose')
}
const onClickPopup = () => { }
const onClickOverlay = () => { }
onMounted(() => {
  //input.value = t('index.user.verificationMessagePlaceholder')
  console.log(props.avatarId)
})
</script>
<style lang="scss" scoped>
:deep(.van-popup) {
  background: none;
}

:deep(.van-cell) {
  background: #a9c3db;
}

:deep(.van-cell__value) {
  font-size: var(--size_24);
}

:deep(.van-field__word-limit) {
  font-size: var(--size_22);
}

.deleteCon {
  width: 900px;

  .top {
    margin: 0 6px;
    height: 80px;
    padding: 0 16px 0 24px;
    display: flex;
    align-items: center;
    background: url('@/assets/images/friend/titleBg.webp') no-repeat;
    background-size: 100% 100%;

    .title {
      color: #f6f5ff;
      font-size: 28px;
      flex: 1;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .bottomCon {
    height: 420px;
    background: url('@/assets/images/friend/conBg.webp') no-repeat;
    background-size: 100% 100%;
    padding: 0 24px;

    .material {
      display: flex;
      padding-top: 16px;

      .info {
        display: flex;
        font-size: var(--size_28);
        font-weight: var(--weight5);
        color: var(--mainTtileColor);
        line-height: 40px;
        padding-left: 16px;

        img {
          padding-left: 8px;
          width: 36px;
          height: 36px;
        }
      }
    }

    .validation {
      font-size: var(--size_24);
      line-height: 35px;
      padding: 28px 0 8px;
    }

    :deep(.van-field__control::placeholder) {
      color: #333333;
    }

    .buttomBtn {
      margin: 16px auto 0;
      width: 212px;
      height: 74px;
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      color: var(--mainTtileColor);
      text-align: center;
      background: url('@/assets/images/friend/confirm.webp') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
