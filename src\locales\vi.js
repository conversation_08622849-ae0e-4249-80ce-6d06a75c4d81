const vi = {
  index: {
    title: 'Trang chủ',
    menuItems: {
      Dynamic: 'Bài viết',
      Favorites: '<PERSON>hu thập',
      Liked: '<PERSON><PERSON> thích',
      Messages: 'Tin nhắn',
      weChat: '<PERSON><PERSON><PERSON>nh khắc',
      myDynamic: '<PERSON><PERSON><PERSON> viết của tôi',
      myFavorites: '<PERSON><PERSON><PERSON> thích của tôi',
      myLiked: '<PERSON>à<PERSON> viết tôi đã thích',
      Hot: 'Xu hướng',
      Recommend: 'Khuyến nghị',
      New: 'Mới nhất',
      Vote: 'Đ<PERSON><PERSON>&Nhận được',
      Game: '2048',
    },
    user: {
      praised: '<PERSON><PERSON><PERSON><PERSON> lượt thích',
      collect: '<PERSON>hu thập',
      chat: 'Tr<PERSON> chuyện',
      addFriends: 'Thêm bạn bè',
      friendRequest: 'Yêu cầu kết bạn',
      verificationMessage: 'Thông tin xác nhận',
      sendNow: '<PERSON><PERSON><PERSON> ngay lập tức',
      verificationMessagePlaceholder: '<PERSON><PERSON> thế nào rồi! Rất vui được gặp bạn',
    },
    msg: {
      likeCollectTab: 'Thích và yêu thích',
      commentAitTab: "Bình luận và {'@'}",
      reply: 'Phản hồi',
      likeCond: 'Tôi đã thích bài viết của bạn',
      likeConc: 'Đã thích bình luận của bạn',
      likeConr: 'Đã thích phản hồi của bạn',
      collectCon: 'Tôi đã lưu bài viết của bạn',
      commentCon: 'Đã bình luận về bài viết của bạn',
      replyCon: 'Đã trả lời',
      aitCon: "{'@'}bạn",
    },
  },
  detail: {
    replyNum: 'Tổng cộng có %num% phản hồi',
    seeMore: 'Xem thêm',
    stop: 'Gấp lại',
    originalText: 'Nội dung gốc',
    translation: 'Dịch',
    commentNum: 'bình luận',
    send: 'Gửi',
    reply: 'Phản hồi',
    replied: 'Đã trả lời',
    //   have:'已有',
    likeListTitle: 'Thích từ %num% người bạn',
  },
  add: {
    title: 'Đăng bài',
    input: 'Vui lòng nhập nội dung bài viết',
    topic: 'Thêm chủ đề',
    whoSee: {
      title: 'Ai có thể xem nó',
      all: 'Mọi người',
      friend: 'Bạn bè có thể thấy',
      oneself: 'Chỉ hiển thị cho chính bạn',
    },
    publish: 'Phát hành',
    audioStart: 'Nhấn để nói',
    audioEnd: 'Nhấn để kết thúc',
    searchPlaceholder: 'Tìm kiếm bạn bè của bạn',
    confirm: 'Xác nhận',
    audioPermission: 'Vui lòng mở quyền microphone trước',
    imagePermission: 'Vui lòng bật quyền camera trước',
    aitUser: "{'@'}người dùng",
  },
  report: {
    next: 'Bước tiếp theo',
    confirmReport: 'Xác nhận báo cáo',
    placeholder: 'Vui lòng nhập nội dung',
    detailTitle: 'Mô tả chi tiết',
    imgTitle: 'Bằng chứng hình ảnh',
    reportSuccess: 'Báo cáo thành công',
    reportFail: 'Báo cáo không thành công',
    reportSuccessInfo:
      'Sau khi gửi, nền tảng sẽ chủ động xác minh và xử lý; cảm ơn bạn đã nỗ lực duy trì môi trường xã hội!',
    reportPublish: 'Quay lại',
    reportOneTitleAfterD: 'Bạn đang báo cáo bài viết của %name%',
    reportOneTitleAfterC: 'Bạn đang báo cáo nhận xét của %name%',
    reportOneTitleAfterR: 'Bạn đang báo cáo câu trả lời của %name%',
    reportTwoTitle: 'Bạn đã chọn %title%',
    reportEndTitleAfterD:
      'Báo cáo của bạn cho bài đăng %name% thuộc về %title%',
    reportEndTitleAfterC:
      'Báo cáo của bạn về ý kiến của %name% thuộc về %title%',
    reportEndTitleAfterR:
      'Báo cáo của bạn về câu trả lời của %name% thuộc về %title%',
  },
  tooltip: {
    delete: 'Xóa',
    modify: 'Chỉnh sửa',
    cancelCollect: 'Bỏ yêu thích',
    report: 'Báo cáo',
    block: 'Chặn',
  },
  delete: {
    deleteCon: 'Bạn có chắc chắn muốn xóa nội dung này không?',
    deleteCancel: 'Hủy bỏ',
    deleteConfirm: 'Xác nhận',
    blockCon: 'Bạn có chắc muốn chặn không?',
  },
  toast: {
    likeSuccess: 'Thích thành công',
    likeCancel: 'Đã hủy bỏ lượt thích',
    likeFail: 'Lượt thích không thành công',
    collectSuccess: 'Yêu thích thành công',
    collectCancel: 'Đã bỏ yêu thích',
    collectFail: 'Yêu thích không thành công',
    publishSuccess: 'Đăng bài thành công',
    publishFail: 'Đăng bài không thành công',
    modifySuccess: 'Chỉnh sửa thành công',
    topicInfo: 'Bạn có thể chọn tối đa 5 chủ đề',
    aitInfo: "Tối đa {'@'}5 người dùng",
    ait: "Vui lòng nhập ít nhất 1 ký tự trước {'@'}",
    dynamicInput:
      'Vui lòng tải lên ít nhất một trong những thứ sau: nội dung động, hình ảnh hoặc video',
    nextInfo: 'Vui lòng chọn một tùy chọn trước',
    reportSuccess: 'Báo cáo thành công',
    reportFail: 'Báo cáo không thành công',
    audioTextSuccess: 'Chuyển giọng nói thành văn bản thành công',
    audioTextFail: 'Chuyển giọng nói thành văn bản không thành công',
    translationSuccess: 'Dịch thành công',
    translationFail: 'Dịch không thành công',
    uploadImageFail: 'Tải lên không thành công',
    deleteSuccess: 'Xóa thành công',
    deleteFail: 'Xóa không thành công',
    // 新加
    imageLimit: 'Kích thước tệp không được vượt quá %num%MB',
    imageNum: 'Có thể tải lên tối đa 9 hình ảnh',
    uploadPrompt: 'Vui lòng nhấp vào để tải lên hình ảnh & video',
    filePrompt:
      '(hỗ trợ định dạng tệp Doc, docx và pdf, kích thước tệp không được vượt quá 5mb)',
    imageBefore: 'Không quá 4mb cho một hình ảnh duy nhất',
    imageShowToast: 'Tải tập tin quá lớn',
    audioFail: 'Lỗi khi kết thúc ghi âm',
    collectCancelFail: 'Hủy bỏ không thành công',
    collectCancelSuccess: 'Hủy bỏ thành công',
    dynamicFail: 'Bài viết không tồn tại',
    addCommentViolation:
      'Nội dung bạn đã gửi bị nghi ngờ vi phạm quy định, vui lòng chỉnh sửa và gửi lại. Chỉnh sửa và gửi lại.',
    addCommentFail: 'Thêm bình luận không thành công',
    addReplyFail: 'Thêm phản hồi không thành công',
    addDynamicViolation:
      'Nội dung "bài viết" bạn đã gửi bị nghi ngờ vi phạm quy định, vui lòng chỉnh sửa và gửi lại.',
    addTopicViolation:
      'Nội dung "chủ đề" bạn đã gửi bị nghi ngờ vi phạm quy định, vui lòng chỉnh sửa và gửi lại.',
    addImageViolation:
      'Nội dung "hình ảnh" bạn đã gửi bị nghi ngờ vi phạm quy định, vui lòng chỉnh sửa và gửi lại.',
    topicCon: 'Nội dung chủ đề không thể để trống',
    getMsgFail: 'Lấy thông tin không thành công',
    loginFail: 'Đăng nhập không thành công',
    aitInfoPermission: 'Hiện tại chỉ hiển thị cho chính bạn',
    alreadyReport:
      'Bạn đã báo cáo nhiều lần, vui lòng chờ phản hồi từ nền tảng',
    commentAfterDelete: 'Bình luận đã bị xóa',
    replyAfterDelete: 'Trả lời đã bị xóa',
    msgDataListFail: 'Thu thập dữ liệu thất bại',
    videoLimit: 'Video không được vượt quá 25 MB',
    videoPrompt: 'Bạn có thể tải lên tối đa 1 video',
    videoToast: 'Chỉ có thể tải lên hình ảnh hoặc video',
    imageTitle: 'Tải lên hình ảnh',
    videoTitle: 'Tải lên video',
    applySuccess: 'Gửi yêu cầu thành công',
    applyFail: 'Gửi yêu cầu không thành công',
    blacklistPrompt: 'Không thể thêm bạn bè từ danh sách đen',
    friendNumPrompt: 'Số lượng bạn bè của người dùng khác đã đầy',
    myNumPrompt: 'Số lượng bạn bè hiện tại đã đầy',
    failedPrompt: 'Lỗi tham số',
    alignPrompt:
      'Bạn đã thêm người này vào danh sách bạn bè, không thể gửi yêu cầu lại',
    applyMyPrompt: 'Không thể tự thêm chính mình',
    alignApply:
      'Bạn đã gửi yêu cầu kết bạn. Bạn có thể gửi yêu cầu khác sau 48 giờ',
    blockSuccess: 'Người dùng đã được thêm vào danh sách đen',
    blockFail: 'Chặn không thành công',
    blockListFull: 'Danh sách chặn đã đầy',
    checkAgreementPrompt:
      'Bạn không đồng ý với《Tuyên bố xuất bản nội dung》, không thể đăng tin',
    AgreementFile: 'Bạn đã đọc và đồng ý với tài liệu này',
    fileTitle: '《Tuyên bố xuất bản nội dung》',
    sameLanguagePrompt:
      'Hiện tại đang sử dụng cùng một ngôn ngữ, không cần dịch',
  },
  vote: {
    voteProgress: 'Đang thực hiện',
    voteEnd: 'Đã kết thúc',
    voteSettle: 'Đã thanh toán',
    oneselfNum: 'Đã bỏ phiếu',
    voteNum: '{num} Đồng',
    timeName: 'Thời gian còn lại',
    allNum: 'Tổng số đồng',
    participateInVoting: 'Tổng số người chơi:',
    getCoins: 'Lần này, bạn đã nhận được {num} xu',
    voteBtn: 'Chọn',
    voteTitle: 'Bỏ phiếu cho {num}',
    inputInfo: 'Vui lòng chọn số lượng',
    voteConfirm: 'Xác nhận',
    voteSuccess: 'Thành công',
    voteFail: 'Thất bại',
    statusEnd: 'Sự kiện đã kết thúc',
    voteTnfo: 'Số lượng xu tối thiểu cần thiết để tham gia sự kiện là 1',
    hold: 'Sở hữu',
    balance:
      'Số dư tài khoản hiện tại của bạn không đủ. Vui lòng nạp tiền kịp thời',
    remainingTimeData: '{days} ngày {hours} giờ {minutes} phút {seconds} giây',
    remainingTimeDaysHours: '{days} ngày {hours} giờ',
    questionInfo:
      'Tất cả các đồng xu của người dùng sẽ đi vào quỹ thưởng của sự kiện này, và những người dùng đoán đúng sẽ chia sẻ tất cả các 【đồng xu thời gian-không gian】 trong quỹ thưởng theo số lượng đồng xu họ đã đoán.',
  },
  video: {
    videoIndex: 'Nói điều gì đó...',
    videoDetail: 'Trang chi tiết',
    videoTitle: 'Video',
  },
  match: {
    statusProgress: 'Đang tiến hành',
    statusEnd: 'Đã kết thúc',
    matchCon: 'Nội dung cuộc thi',
    matchRule: 'Quy tắc của cuộc thi',
    matchAward: 'Phần thưởng cuộc thi',
    matchInstructions: 'Hướng dẫn cạnh tranh',
    matchWorks: 'Sản xuất',
    matchTitle: 'Cuộc thi',
    matchTime: 'Thời gian đăng ký',
    myMatch: 'Cuộc thi của tôi',
    statusWaiting: 'Đang chờ bắt đầu',
    vote: 'Bầu phiếu',
    voted: 'Đã bỏ phiếu',
    voteNum: '{num} phiếu',
    uploadWorks: 'Tải lên tác phẩm của bạn',
    matchAddTitle: 'Tiêu đề',
    titlePlaceholder: 'Vui lòng nhập tiêu đề',
    submit: 'Nộp',
    content: 'Nội dung',
    voteConfirm: 'Bạn có muốn bỏ phiếu cho tác phẩm của {name} không?',
    voteSuccess: 'Bầu phiếu thành công',
    voteFail: 'Bầu phiếu thất bại',
    imageShowToast: 'Tiêu đề và hình ảnh không được để trống',
    videoShowToast: 'Tiêu đề và video không được để trống',
    addWorkTitle:
      'Nội dung của "tiêu đề" đã gửi bị nghi ngờ vi phạm quy tắc. Vui lòng sửa đổi và gửi lại.',
    addWorkContent:
      'Nội dung của "nội dung" đã gửi bị nghi ngờ vi phạm quy tắc. Vui lòng sửa đổi và gửi lại.',
    againAddWork:
      'Bạn đã tham gia cuộc thi hiện tại và không thể tham gia lại!',
    myMatchTitle: 'Tác phẩm của tôi',
    otherMatchTitle: 'Tác phẩm của người khác',
  },
  empty: {
    comment: 'Chưa có bình luận nào',
    list: 'Không có dữ liệu',
    content: 'Không có nội dung',
    message: 'Không có tin nhắn nào',
  },
  game:{
    myCardTitle:`Bài của tôi.`,
    merge:`Hợp thành`,
    maskObtaining:`Chúc mừng chiến thắng`,
    maskInfo:`Nhanh lên và thêm bạn bè để tổng hợp những cái thẻ mới!`,
    clickAnywhere:`Click bất cứ nơi nào để tiếp tục`,
    mergeSuccess:`Tổng hợp thành công`,
    newFriends:`Bạn mới`,
    receiveTitle:`Xin chúc mừng!`,
    receiveInfo:`Sau khi tuyên bố thành công, 2048 sẽ biến mất!`,
    receiveImgText:`Thẻ truyền tải`,
    receivePublish:`Đi đến hòm thư để nhận phần thưởng`,
    shop:`Cửa hàng`,
    purchaseCards:`Mua thẻ`,
    totalAmount:`Tổng số tiền`,
    purchase:`Mua hàng`,
    selectCards:`Chọn thẻ`,
    purchaseSuccessful:`Mua thành công`,
    purchaseFailed:`Mua bán thất bại`,
    synthesisFailed:`Tổng hợp thất bại`,
    claimSuccessful:`Tuyên bố thành công`,
    claimFailed:`Tuyên bố thất bại`,
    exchangeTips:`Bạn có thẻ 2048! Chuyển đổi nó cho một thẻ Teleport!`,
    rule:`Luật chơi`,
    ruleNum1:`1.Khi bạn bắt đầu trò chơi, bạn sẽ nhận được một thẻ cơ bản ngẫu nhiên với 2 màu sắc`,
    ruleNum2:`2.Thêm bạn bè với những người dùng khác có cùng số, thẻ màu khác nhau, có thể tổng hợp thẻ cao cấp hơn`,
    ruleNum3:`3.Sau khi tổng hợp thành công, thẻ của hai tài khoản ban đầu được nâng cấp lên thẻ mới với số gấp đôi.`,
    ruleNum4:`4.Khi số trên thẻ đạt 2048, bạn có thể đổi thẻ truyền một thẻ`,
    ruleNum5:`5.Nguyên nhân tổng hợp thất bại có vài loại sau: bạn chọn nâng cấp thẻ màu sắc cùng màu với đối phương; Thẻ đã được người khác sử dụng; Bạn và đối phương đã là quan hệ bạn tốt; Số lượng bạn bè của bạn đã đến giới hạn; Số lượng bạn tốt của đối phương đã đến giới hạn cao nhất.`
  }
}

export default vi
