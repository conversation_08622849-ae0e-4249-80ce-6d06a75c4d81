<template>
  <div>
    <!-- @loadedmetadata="getPoster" width="100%" height="240px"-->
    <video
      id="my-player"
      ref="videoRef"
      class="video-js"
      :class="className"
      controls
      preload="auto"
      data-setup="{}"
      :poster="imageData"
      @play="handleVideoPlay"
      @pause="handleVideoPause"
      @ended="handleVideoEnd"
      controlslist="nodownload noremoteplayback"
    >
      <source :src="data" type="video/mp4" />
      <source :src="data" type="video/webm" />
      <p class="vjs-no-js">
        To view this video please enable JavaScript, and consider upgrading to a
        web browser that
        <a href="https://videojs.com/html5-video-support/" target="_blank">
          supports HTML5 video
        </a>
      </p>
    </video>
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { videoState } from '@/utils/useVideoState.js'
const props = defineProps({
  data: {
    type: String,
    default: '',
  },
  imageData: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
})
// 为模板引用标注类型
const videoRef = ref(null)
const veoPoster = ref('')
const getPoster = () => {
  // console.log(videoRef.value)
  // 在未设置封面时，自动截取视频0.5s对应帧作为视频封面
  // 由于不少视频第一帧为黑屏，故设置视频开始播放时间为0.5s，即取该时刻帧作为封面图
  if (videoRef.value) {
    videoRef.value.currentTime = 0.5
    // 创建canvas元素
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    // canvas画图
    canvas.width = videoRef.value.videoWidth
    canvas.height = videoRef.value.videoHeight
    ctx?.drawImage(videoRef.value, 0, 0, canvas.width, canvas.height)
    // 把canvas转成base64编码格式
    veoPoster.value = canvas.toDataURL('image/png')
    // console.log(veoPoster.value)
    videoRef.value.currentTime = 0
  } else {
  }
}
// 视频事件处理
const handleVideoPlay = () => {
  console.log('开始播放')
  videoState.pauseOthers(videoRef.value)
}

const handleVideoPause = () => {
  if (videoState.activeVideo === videoRef.value) {
    videoState.activeVideo = null
  }
}

const handleVideoEnd = () => {
  videoState.activeVideo = null
  videoRef.value.currentTime = 0
}

// 可视区域控制（保留原有功能）
let observer = null
let wasPlaying = false // 记录离开视口前的播放状态
onMounted(() => {
  // 初始化 Intersection Observer
  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (!entry.isIntersecting) {
          // 离开视口时暂停并记录状态
          wasPlaying = !videoRef.value.paused
          if (wasPlaying) {
            videoRef.value.pause()
          }
          videoRef.value.currentTime = 0 // 重置播放进度
        } else {
          // 进入视口时根据之前状态恢复播放
          // if (wasPlaying) {
          //     videoRef.value.play().catch(() => { }); // 处理自动播放限制
          // }
        }
      })
    },
    {
      threshold: 0.5,
    },
  )

  observer.observe(videoRef.value)
})

onBeforeUnmount(() => {
  if (observer) observer.disconnect()
  if (videoState.activeVideo === videoRef.value) {
    videoState.activeVideo = null
  }
})
</script>
<style scoped lang="scss">
.video-js {
  width: 100%;
  height: 348px;
}

.videoWH {
  width: 100%;
  height: 240px;
}
</style>
