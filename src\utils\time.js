// 年月日.
export function ymdDianTime(data) {
  var date = new Date(data.replace(/-/g, '/'))
  // var date = new Date(data)
  var Y = date.getFullYear()
  var M = date.getMonth() + 1
  M = M < 10 ? '0' + M : M
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  return Y + '.' + M + '.' + d
}
export function createTime(data) {
  var date = new Date(data)
  var Y = date.getFullYear()
  var M = date.getMonth() + 1
  M = M < 10 ? '0' + M : M
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var m = date.getMinutes()
  m = m < 10 ? '0' + m : m
  var s = date.getSeconds()
  s = s < 10 ? '0' + s : s

  return Y + '.' + M + '.' + d + ' ' + h + ':' + m + ':' + s
}
export function isOnlyEmoji(data) {
  // Unicode表情符号的正则表达式
  const emojiRegex = /\p{Extended_Pictographic}/u
  // 检查字符串是否只包含Emoji
  return data.length > 0 && [...data].every((char) => emojiRegex.test(char))
}
/**
 * 计算剩余时间并国际化显示
 * @param {string|number} stopTime - 结束时间戳或日期字符串
 * @returns {string} - 格式化后的剩余时间
 */
export function getTimeRemaining(stopTime) {
  // 转换结束时间为时间戳
  const endTime = new Date(stopTime).getTime()
  const currentTime = new Date().getTime()

  // 如果已经结束，返回全部为0
  if (currentTime >= endTime) {
    return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
    }
  }

  // 计算时间差（毫秒）
  const timeDiff = endTime - currentTime

  // 计算天、时、分、秒
  let days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
  let hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  let minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
  let seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

  // 格式化为两位数字
  days = days < 10 ? '0' + days : days.toString()
  hours = hours < 10 ? '0' + hours : hours.toString()
  minutes = minutes < 10 ? '0' + minutes : minutes.toString()
  seconds = seconds < 10 ? '0' + seconds : seconds.toString()

  return {
    days,
    hours,
    minutes,
    seconds,
  }
}
