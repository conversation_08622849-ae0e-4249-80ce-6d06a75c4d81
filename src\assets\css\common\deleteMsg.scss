:deep(.van-popup--center) {
  border-radius: 8px;
}

.deleteCon {
  text-align: center;
  width: 484px;
  padding: 40px 70px;
  border-radius: 8px;
  position: relative;

  .closeImg {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 16px;
    right: 24px;
  }

  .info {
    margin-top: 40px;
    font-size: 30px;
    color: #666666;
    overflow-wrap: break-word;
    white-space: pre-wrap; /* 或 normal */
  }

  .title {
    padding: 28px 0 48px;
    color: #333333;
    line-height: 40px;
    font-size: 30px;
    font-weight: 500;
  }

  .buttom {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 23px;
    font-size: 30px;

    .buttomBtn {
      width: 100%;
      height: 74px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      color: #222222;
      text-align: center;
      background: #e8e8e8;
    }

    .activeBtn {
      color: #ffffff;
      background: #5195e7;
    }
  }
}
