<template>
  <div>
    <van-popup v-model:show="dialogVisible" title="" closeable>
      <div class="deleteCon">
        <div class="title">{{ $t('game.exchangeTips') }}</div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue'
const dialogVisible = ref(false)
const props = defineProps({
  dialogMerge: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogMerge,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)
</script>
<style lang="scss" scoped>
@import '@/assets/css/common/deleteMsg.scss';
.deleteCon {
  background: #D2E3F3;
  .title {
    padding: 30px;
  }
}
</style>
