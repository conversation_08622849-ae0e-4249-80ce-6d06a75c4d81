import seleteLike from '@/assets/images/index/selectLove.png'
import noSeleteLike from '@/assets/images/index/noSelectLove.png'

import seleteCollect from '@/assets/images/index/selectCollect.png'
import noSeleteCollect from '@/assets/images/index/noSelectCollect.png'

import seleteDetailLike from '@/assets/images/detail/selectLove.png'
import noSeleteDetailLike from '@/assets/images/detail/noSelectLove.png'

import seleteDetailCollect from '@/assets/images/detail/selectCollect.png'
import noSeleteDetailCollect from '@/assets/images/detail/noSelectCollect.png'

import seleteDetailLikeComment from '@/assets/images/detail/selectLoveComment.png'
import noSeleteDetailLikeComment from '@/assets/images/detail/noSelectLoveComment.png'
import seleteVideoLikeLike from '@/assets/images/video/selectLove.webp'
import noSeleteVideoLikeLike from '@/assets/images/video/noSelectLove.webp'
import seleteVideoLikeCollect from '@/assets/images/video/selectCollect.webp'
import noSeleteVideoLikeCollect from '@/assets/images/video/noSelectCollect.webp'
// 选中不选中点赞
export const isSeleteLike = {
  selete: seleteLike,
  noselete: noSeleteLike,
}
// 选中不选中收藏
export const isSeleteCollect = {
  selete: seleteCollect,
  noselete: noSeleteCollect,
}
// 选中不选中详情点赞
export const isSeleteDetailLike = {
  selete: seleteDetailLike,
  noselete: noSeleteDetailLike,
}
// 选中不选中详情收藏
export const isSeleteDetailCollect = {
  selete: seleteDetailCollect,
  noselete: noSeleteDetailCollect,
}
// 选中不选中详情评论点赞
export const isSeleteDetailLikeComment = {
  selete: seleteDetailLikeComment,
  noselete: noSeleteDetailLikeComment,
}
// 选中不选中视频点赞
export const isSeleteVideoLike = {
  selete: seleteVideoLikeLike,
  noselete: noSeleteVideoLikeLike,
}
// 选中不选中视频收藏
export const isSeleteVideoCollect = {
  selete: seleteVideoLikeCollect,
  noselete: noSeleteVideoLikeCollect,
}
