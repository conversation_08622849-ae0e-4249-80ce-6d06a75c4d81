<template>
  <div class="item">
    <div class="sid">ID:{{ data.id }}</div>
    <div class="title">{{ data.title }}</div>
    <div class="top" v-if="data.status == 1">
      <div class="time">
        {{ $t('vote.timeName') }}：{{
          $t('vote.remainingTimeData', {
            days: remainingTime.days,
            hours: remainingTime.hours,
            minutes: remainingTime.minutes,
            seconds: remainingTime.seconds,
          })
        }}
      </div>
      <div class="allTicket">{{ $t('vote.allNum') }}：{{ data.voteTotal }}</div>
    </div>
    <div class="pkResult" v-if="data.status == 4">
      <div class="pkCon">
        <img :src="leftUrl" />
        <img :src="rightUrl" />
      </div>
    </div>
    <div class="pk">
      <div class="pkLeft"></div>
      <div class="square">
        <div class="squareCon">
          <div class="name">{{ data.item1.title }}</div>
          <div class="num">
            <div :class="visibilityClass" class="leftNum">
              {{ $t('vote.voteNum', { num: data.item1.votes }) }}
            </div>
            <div v-if="data.item1.coin">
              {{ $t('vote.oneselfNum') }}:{{ data.item1.coin }}
            </div>
          </div>
        </div>
      </div>
      <img class="pkImg" src="@/assets/images/vote/pk.webp" />
      <div class="negative">
        <div class="negativeCon">
          <div class="name">{{ data.item2.title }}</div>
          <div class="num">
            <div v-if="data.item2.coin" class="leftNum">
              {{ $t('vote.oneselfNum') }}:{{ data.item2.coin }}
            </div>
            <div :class="visibilityClass">
              {{
                $t('vote.voteNum', {
                  num: data.item2.votes,
                })
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="voteBtn" v-if="data.status == 1">
      <div class="btn" @click="openVote(data.item1, data.id)">
        {{ $t('vote.voteBtn') }}
      </div>
      <div class="btn negativeBtn" @click="openVote(data.item2, data.id)">
        {{ $t('vote.voteBtn') }}
      </div>
    </div>
    <div class="top" v-else>
      <!-- <div class="time">{{ $t('vote.timeName') }}：{{ time(data.stopTime) }}</div> -->
      <div class="allTicket">{{ $t('vote.allNum') }}：{{ data.voteTotal }}</div>
    </div>
    <div class="summary" :class="data.status == 4 ? 'top' : ''">
      <div class="time">
        {{ $t('vote.participateInVoting') }}{{ data.userTotal }}
      </div>
      <div class="allTicket" v-if="data.status == 4">
        {{ $t('vote.getCoins', { num: data.winCoinTotal }) }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { createTime, getTimeRemaining } from '@/utils/time.js'
import winVote from '@/assets/images/vote/win.webp'
import lossVote from '@/assets/images/vote/loss.webp'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    },
  },
  index: {
    type: Number,
    default: 0,
  },
})
const remainingTime = ref({
  days: '00',
  hours: '00',
  minutes: '00',
  seconds: '00',
})
let timerInterval = null
const leftUrl = computed(() => {
  // 在这里定义你的计算逻辑
  if (props.data.item1.result == 1) {
    return winVote
  } else if (props.data.item1.result == 2) {
    return lossVote
  }
})
const rightUrl = computed(() => {
  // 在这里定义你的计算逻辑
  if (props.data.item2.result == 1) {
    return winVote
  } else if (props.data.item2.result == 2) {
    return lossVote
  }
})
// 在 script 部分
const visibilityClass = computed(() => {
  return props.data.item1.coin || props.data.item2.coin ? '' : 'visiblity'
})
// 计算时间
const time = (data) => {
  return createTime(data)
}
const updateRemainingTime = () => {
  remainingTime.value = getTimeRemaining(props.data.stopTime)
}
const emits = defineEmits(['openVotePopup'])
const openVote = (data, id) => {
  console.log(data)
  emits('openVotePopup', data, id)
}
onMounted(() => {
  // 初始计算
  updateRemainingTime()
  // 每秒更新一次
  timerInterval = setInterval(updateRemainingTime, 1000)
  // if (props.data.item1.result == 1) {
  //     leftUrl.value = winVote
  // } else if (props.data.item1.result == 2) {
  //     leftUrl.value = loss
  // }
  // if (props.data.item2.result == 1) {
  //     rightUrl.value = winVote
  // } else if (props.data.item2.result == 2) {
  //     rightUrl.value = loss
  // }
})
onBeforeUnmount(() => {
  // 组件销毁前清除定时器
  if (timerInterval) {
    clearInterval(timerInterval)
  }
})
</script>
<style lang="scss" scoped>
@import '@/assets/css/index/voteItem.scss';
</style>
