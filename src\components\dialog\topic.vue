<template>
  <van-popup
    v-model:show="dialogVisible"
    :showConfirmButton="false"
    :close-on-click-overlay="false"
  >
    <div class="con">
      <div class="top">
        <div class="title">{{ $t('add.topic') }}</div>
        <img @click="close" src="@/assets/images/index/close.png" />
      </div>
      <div class="labelMore">
        <van-field
          rows="4"
          v-model="input"
          :maxlength="20"
          :placeholder="$t('add.topic')"
        />
        <img @click="audio" src="@/assets/images/index/autio.png" />
      </div>

      <div class="publish" @click="publish">
        {{ $t('add.confirm') }}
      </div>
      <AudioRecorder
        :isShowAudio="isShowAudio"
        @dialogCloseAudio="dialogCloseAudio"
        @audioSuccess="audioSuccess"
      >
      </AudioRecorder>
    </div>
  </van-popup>
</template>
<script setup>
import { ref, watch, onBeforeUnmount } from 'vue'
import { showToast } from 'vant'
import AudioRecorder from '@/components/common/audioRecorder.vue'
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const dialogVisible = ref(false)
const input = ref('')
const isShowAudio = ref(false)
const props = defineProps({
  dialogFormTopic: {
    type: Boolean,
    require: false,
    default: false,
  },
})
watch(
  () => props.dialogFormTopic,
  (val) => {
    // console.log(val)
    dialogVisible.value = val
  },
  { immediate: true },
)
// 定义组件的事件
const emits = defineEmits(['dialogCloseTopic', 'confirmAdd'])
const close = () => {
  emits('dialogCloseTopic', false)
  input.value = ''
}
// 点击语音
const audio = () => {
  window.location.href = 'uniwebview://requestpermission'
  // isShowAudio.value = true
  countStore.audioType = 'topic'
  // emitter.emit('clickToic')
}

emitter.on('openShowAudio', (audioType, data) => {
  if (audioType == 'topic') {
    isShowAudio.value = true
  }
})
const dialogCloseAudio = (data) => {
  console.log('关闭')
  isShowAudio.value = false
}
// 录音成功
const audioSuccess = (data) => {
  console.log('转文字成功', data)
  input.value = input.value + data
}
const publish = () => {
  if (input.value.trim()) {
    emits('dialogCloseTopic', false)
    emits('confirmAdd', input.value)
    input.value = ''
  } else {
    showToast(t('toast.topicCon'))
  }
}
onBeforeUnmount(() => {
  emitter.off('openShowAudio')
})
</script>
<style lang="scss" scoped>
.con {
  padding: 26px 30px;

  .top {
    font-weight: var(--weight5);
    font-size: var(--size_28);
    color: var(--mainTileColor);
    line-height: 40px;
    margin-bottom: 16px;
    display: flex;

    .title {
      flex: 1;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .labelMore {
    display: flex;
    align-items: center;

    img {
      margin-left: 20px;
      width: 38px;
      height: 38px;
    }
  }

  .publish {
    float: right;
    margin: 52px 0 30px;
    // width: 152px;
    padding: 0 50px;
    height: 62px;
    line-height: 62px;
    text-align: center;
    color: #c4e5ff;
    font-size: var(--size_28);
    font-weight: var(--weight5);
    background: url('@/assets/images/index/publish.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
