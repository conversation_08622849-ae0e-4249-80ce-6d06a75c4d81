import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/Layout.vue'

// 创建路由历史记录数组
const routeHistory = []

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      redirect: '/index',
      component: Layout,
      children: [
        {
          path: '/index',
          name: 'index',
          component: () => import('@/views/index/index.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/home',
          name: 'home',
          component: () => import('@/views/index/home.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/reportOne',
          name: 'reportOne',
          component: () => import('@/views/report/reportOne.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/reportTwo',
          name: 'reportTwo',
          component: () => import('@/views/report/reportTwo.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/reportEnd',
          name: 'reportEnd',
          component: () => import('@/views/report/reportEnd.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/term/serve',
          name: 'term/serve',
          component: () => import('@/views/term/serve.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/match',
          name: 'match',
          component: () => import('@/views/match/index.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/matchDetail',
          name: 'matchDetail',
          component: () => import('@/views/match/detail.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/myMatch',
          name: 'myMatch',
          component: () => import('@/views/match/myMatch.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/video',
          name: 'video',
          component: () => import('@/views/video/index.vue'),
          meta: {
            keepAlive: false,
            dynamicKeepAlive: true,
          },
        },
        {
          path: '/shop',
          name: 'shop',
          component: () => import('@/views/game/shop.vue'),
          meta: {
            keepAlive: false,
          },
        },
      ],
    },
  ],
})

// 全局前置守卫，用于记录路由历史
router.beforeEach((to, from, next) => {
  // 记录路由历史
  routeHistory.push(from.path)
  // 限制历史记录长度，防止内存泄漏
  if (routeHistory.length > 10) {
    routeHistory.shift()
  }
  // console.log('路由历史:', routeHistory);

  // 继续导航
  next()
})

// 导出路由历史记录，以便其他组件可以使用
export { routeHistory }

export default router
