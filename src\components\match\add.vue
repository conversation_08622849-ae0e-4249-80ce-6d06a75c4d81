<template>
  <div>
    <van-popup
      v-model:show="dialogVisible"
      round
      :close-on-click-overlay="false"
    >
      <div class="item">
        <div class="top">
          <div class="title">{{ $t('match.uploadWorks') }}</div>
          <img @click="close" src="@/assets/images/index/close.png" />
        </div>
        <div class="con">
          <div class="title">{{ $t('match.matchAddTitle') }}</div>
          <van-field
            v-model="title"
            type="textarea"
            maxlength="40"
            show-word-limit
            :placeholder="$t('match.titlePlaceholder')"
          />
          <div class="title" v-if="type == '1'">{{ $t('match.content') }}</div>
          <div class="input" v-if="type == '1'">
            <van-field
              v-model="textarea"
              rows="5"
              type="textarea"
              maxlength="2000"
              show-word-limit
              :placeholder="$t('add.input')"
            />
          </div>
          <div class="prompt" v-if="type == '1'">
            <span class="title">{{ $t('toast.imageTitle') }}</span
            ><span>{{ $t('toast.imageNum') }}</span>
          </div>
          <g-upload
            :mode="imgList"
            v-if="type == '1'"
            @chooseFile="chooseImage"
            @imgDelete="imgDelete"
            :maxCount="9"
            type="report"
          ></g-upload>
          <div class="prompt" v-if="type == '2'">
            <span class="title">{{ $t('toast.videoTitle') }}</span
            >{{ $t('toast.videoLimit') }}{{ $t('toast.videoPrompt') }}
          </div>
          <g-video
            v-if="type == '2'"
            @chooseFile="chooseVideo"
            @videoDelete="videoDelete"
            :maxCount="1"
          ></g-video>
          <div class="declaration">
            <van-checkbox
              v-model="checked"
              checked-color="#469DF4"
              shape="square"
              label-disabled
              >{{ $t('toast.AgreementFile')
              }}<span
                ><a @click="gotoDetail('/term/serve')">{{
                  $t('toast.fileTitle')
                }}</a></span
              ></van-checkbox
            >
          </div>
          <div class="publish" @click="publish">{{ $t('match.submit') }}</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import { showToast, closeToast, showLoadingToast } from 'vant'
import gUpload from '@/components/common/g-upload.vue'
import gVideo from '@/components/common/g-video.vue'
import emitter from '@/utils/mitt.js'
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import { publishWorks } from '@/api/match.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { useRouter } from 'vue-router'
const router = useRouter()
import _ from 'lodash'
const props = defineProps({
  dialogFormAdd: {
    type: Boolean,
    require: false,
    default: false,
  },
  id: {
    type: String,
    require: false,
    default: '',
  },
  type: {
    type: String,
    require: false,
    default: '',
  },
})
const dialogVisible = ref(false)
const textarea = ref('')
const title = ref('')
const imgList = ref([])
const imageList = ref([])
const videoUrl = ref('')
const videoFrame = ref('')
const checked = ref(true)
onMounted(() => {
  console.log('打开add')
  console.log(props.type)
})

watch(
  () => props.dialogFormAdd,
  (val) => {
    // console.log(val)
    dialogVisible.value = val
  },
  { immediate: true },
)
const gotoDetail = (data) => {
  // 跳转到网页隐私政策页面
  router.push({ path: data })
}
// 定义组件的事件
const emits = defineEmits(['dialogCloseAdd', 'addSuccess'])
const close = () => {
  emits('dialogCloseAdd', false)
}
// 上传视频改变
const chooseVideo = (data, posterData) => {
  // console.log(data,posterData)
  videoUrl.value = data
  videoFrame.value = posterData
  emitter.emit('changeImageDisabled', true)
}
// 删除视频
const videoDelete = (data) => {
  console.log(data)
  videoUrl.value = ''
  videoFrame.value = ''
  emitter.emit('changeImageDisabled', false)
}
// 上传图片改变
const chooseImage = (data) => {
  console.log('gaibiangaibian')
  console.log(data)
  // 判断
  imageList.value.push(data)
  console.log(data)
  emitter.emit('changeVideoDisabled', true)
}
const imgDelete = (data) => {
  console.log(data)
  imageList.value.splice(data.index, 1)
  console.log(imageList.value)
  showToast(t('toast.deleteSuccess'))
  if (imageList.value.length == 0) {
    emitter.emit('changeVideoDisabled', false)
  }
}
// 发布
const publish = () => {
  // 根据type判断是发布图片还是发布视频
  if (props.type == '1') {
    if (title.value.length > 0 && imageList.value.length > 0) {
      if (checked.value) {
        // 添加动态
        addDynamics()
      } else {
        showToast(t('toast.checkAgreementPrompt'))
      }
    } else {
      // 提示必填项 动态内容不能为空
      showToast(t('match.imageShowToast'))
    }
  } else {
    if (title.value.length > 0 && videoUrl.value.length > 0) {
      if (checked.value) {
        // 添加动态
        addDynamics()
      } else {
        showToast(t('toast.checkAgreementPrompt'))
      }
    } else {
      // 提示必填项 动态内容不能为空
      showToast(t('match.videoShowToast'))
    }
  }
}
const addDynamics = _.debounce(
  () => {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    })
    // 根据type判断是发布图片还是发布视频
    const data = {
      contestId: props.id,
      userId: countStore.loginData.roleId,
      voteType: props.type,
      voteTitle: title.value,
      voteContent: textarea.value,
      imageList: imageList.value,
      voteVideoFrame: videoFrame.value,
      voteVideo: videoUrl.value,
    }
    console.log(data)
    publishWorks(data)
      .then((res) => {
        console.log(res)
        closeToast()
        if (res.code == 200) {
          showToast(t('toast.publishSuccess'))
          emits('dialogCloseAdd', false)
          emits('addSuccess')
          ta.track('addDynamic', {
            count: 1,
            image_count: imageList.value.length > 0 ? 1 : 0,
            content_count: textarea.value.length > 0 ? 1 : 0,
          })
        } else if (res.code === 500) {
          if (res.msg == '1') {
            // 标题
            showToast({
              message: t('match.addWorkTitle'),
            })
          } else if (res.msg == '2') {
            // 内容
            showToast({
              message: t('match.addWorkContent'),
            })
          } else if (res.msg == '3') {
            // 图片
            showToast({
              message: t('toast.addImageViolation'),
            })
          } else {
            showToast(t('toast.publishFail'))
          }
        } else {
          showToast(t('toast.publishFail'))
        }
      })
      .catch(function (error) {
        closeToast()
        console.log(error)
        showToast(t('toast.publishFail'))
      })
  },
  1000,
  { leading: true, trailing: false },
)
onBeforeUnmount(() => {
  emitter.off('changeValue')
  emitter.off('openShowAudio')
})
</script>
<style scoped lang="scss">
:deep(.van-popup) {
  width: 90%;
}

:deep(.van-popup--center) {
  max-width: 90%;
}

:deep(.van-cell) {
  background: #f3f3f3;
}

:deep(.van-field__control) {
  font-size: var(--size_26);
  line-height: 37px;
}

.item {
  height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .top {
    height: 80px;
    display: flex;
    font-size: 28px;
    justify-content: center;
    align-items: center;
    padding: 0 24px 0 30px;

    .title {
      margin-top: 30px;
      flex: 1;
      color: var(--mainTtileColor);
      font-size: var(--size_28);
      font-family: JLinXin;
      line-height: 48px;
      font-weight: 550;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .con {
    padding: 0 30px 0;
    position: relative;
    flex: 1;

    .title {
      font-size: 26px;
      color: #333333;
      margin: 20px 0;
    }

    .prompt {
      margin: 20px 0;
      font-size: var(--size_20);
      color: var(--bearTextColor);
      display: flex;
      align-items: center;

      .title {
        color: var(--mainTtileColor);
        font-weight: 500;
        font-size: var(--size_26);
        padding-right: 30px;
      }
    }

    .declaration {
      margin: 20px 0;
      display: flex;
      justify-content: center;
      font-size: var(--size_20);
      color: var(--bearTextColor);

      span {
        color: #469df4;
      }

      :deep(.van-checkbox__icon) {
        font-size: 25px;
      }
    }

    .publish {
      position: absolute;
      bottom: 10px;
      right: 28px;
      // width: 152px;
      height: 62px;
      display: flex;
      align-items: center;
      text-align: center;
      color: #c4e5ff;
      font-size: var(--size_28);
      font-weight: var(--weight5);
      background: url('@/assets/images/index/publish.png') no-repeat;
      background-size: 100% 100%;
      padding: 0 30px;
    }
  }
}
</style>
