<template>
  <div class="game-card" :class="[colorClass, cardTypeClass]">
    <div class="card-container" @click="handleClick">
      <div class="corner top-left">{{ number }}</div>
      <div class="center">
        <div class="circle">
          <span class="number">{{ number }}</span>
        </div>
      </div>
      <div class="corner bottom-right">{{ number }}</div>
    </div>
  </div>
</template>

<script setup>
import id from '@/locales/id'
import { computed } from 'vue'

// 定义接受的属性
const props = defineProps({
  // 卡牌数字
  number: {
    type: [Number, String],
    default: 2,
  },
  // 卡牌颜色，支持数字 1-8
  color: {
    type: Number,
    default: 4,
    validator: (value) => {
      const validNumbers = [1, 2, 3, 4, 5, 6, 7, 8]
      return validNumbers.includes(value)
    },
  },
  id: {
    type: Number,
    default: 0,
  },
  // 卡牌类型，影响卡牌尺寸
  cardType: {
    type: String,
    default: 'normal', // 默认普通尺寸
    validator: (value) => {
      return [
        'normal',
        'my',
        'toast',
        'shop',
        'buyToast',
        'mergeToast',
        'receiveToast',
      ].includes(value)
    },
  },
})

// 数字到颜色名称的映射表
const numberToColorMap = {
  1: 'red',
  2: 'orange',
  3: 'yellow',
  4: 'green',
  5: 'cyan',
  6: 'blue',
  7: 'purple',
  8: 'pink',
}

// 计算颜色类名
const colorClass = computed(() => {
  // color 现在只接受数字，直接映射到颜色名称
  const colorName = numberToColorMap[props.color]
  return `color-${colorName}`
})

// 计算卡牌类型类名
const cardTypeClass = computed(() => {
  return `type-${props.cardType}`
})
const emits = defineEmits(['openReceiveMask'])
// 点击卡牌
const handleClick = () => {
  console.log('点击卡片')
  if (props.number == 2048 && props.cardType == 'my') {
    //   向父组件传递打开mask
    const data = {
      cardColor: props.color,
      cardNumber: props.number,
      id: props.id,
    }
    emits('openReceiveMask', data)
  }
}
</script>

<style scoped lang="scss">
.game-card {
  position: relative;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-sizing: border-box;
  // overflow: hidden;
  background: linear-gradient(203deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 0.62) 100%);
  // 默认尺寸 (normal)
  width: 40px;
  height: 56px;

  .card-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    overflow: hidden;
  }

  // my类型卡牌 (68px × 92px)
  &.type-my {
    width: 68px;
    height: 92px;
    border: 4px solid #ffffff;

    .corner {
      font-size: 4px;
    }

    .center .circle {
      width: 50px;
      height: 50px;

      .number {
        font-size: 18px;
      }
    }
  }

  // buyToast类型卡牌 (119px × 176px)
  &.type-buyToast {
    width: 119px;
    height: 176px;
    border: 4px solid #ffffff;

    .corner {
      font-size: 10px;
    }

    .center .circle {
      width: 94px;
      height: 94px;

      .number {
        font-size: 26px;
      }
    }
  }

  // shop类型卡牌 (140px × 190px)
  &.type-shop {
    width: 140px;
    height: 190px;
    border: 8px solid #ffffff;
    border-radius: 8px;

    .card-container {
      border-radius: 6px;
    }

    .corner {
      font-size: 22px;
    }

    .center .circle {
      width: 98px;
      height: 98px;

      .number {
        font-size: 60px;
        text-shadow: 2px 2px 0px #000000;
      }
    }
  }

  // buyToast类型卡牌 (158px × 216px)
  &.type-mergeToast {
    width: 158px;
    height: 216px;
    border: 8px solid #ffffff;

    .corner {
      font-size: 22px;
    }

    .center .circle {
      width: 98px;
      height: 98px;

      .number {
        font-size: 60px;
      }
    }
  }

  // receiveToast类型卡牌 (284px × 388px)
  &.type-receiveToast {
    width: 276px;
    height: 370px;
    background-color: #fff;
    border-radius: 18px;
    padding: 12px;

    .card-container {
      border-radius: 6px;
    }

    .corner {
      font-size: 34px;
    }

    .center .circle {
      width: 240px;
      height: 240px;

      .number {
        font-size: 100px;
        text-shadow: 2px 2px 0px #000000;
      }
    }
  }

  // toast类型卡牌 (330px × 452px)
  &.type-toast {
    width: 330px;
    height: 452px;
    background-color: #fff;
    border-radius: 18px;
    padding: 12px;

    .card-container {
      border-radius: 6px;
    }

    .corner {
      font-size: 34px;
    }

    .center .circle {
      width: 260px;
      height: 260px;

      .number {
        font-size: 110px;
        text-shadow: 2px 2px 0px #000000;
      }
    }
  }

  .corner {
    position: absolute;
    font-size: 12px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
    z-index: 1;

    &.top-left {
      top: 2px;
      left: 2px;
    }

    &.bottom-right {
      bottom: 2px;
      right: 2px;
      transform: rotate(180deg);
    }
  }

  .center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 0;

    .circle {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

      .number {
        font-size: 18px;
        font-weight: bold;
        text-shadow: 2px 2px 0px #000000;
      }
    }
  }

  // 为每种颜色定义不同的数字颜色
  &.color-red .circle .number {
    color: #d6261f;
  }

  &.color-blue .circle .number {
    color: #0392da;
  }

  &.color-yellow .circle .number {
    color: #fef503;
  }

  &.color-green .circle .number {
    color: #06933c;
  }

  &.color-purple .circle .number {
    color: #9e08d3;
  }

  &.color-orange .circle .number {
    color: #ff7800;
  }

  &.color-cyan .circle .number {
    color: #00c5a7;
  }

  &.color-pink .circle .number {
    color: #ff4eb3;
  }

  // 为每种颜色定义卡牌背景色 - 应用到card-container
  &.color-red .card-container {
    background-color: #d6261f;
  }

  &.color-blue .card-container {
    background-color: #0392da;
  }

  &.color-yellow .card-container {
    background-color: #fef503;
  }

  &.color-green .card-container {
    background-color: #06933c;
  }

  &.color-purple .card-container {
    background-color: #9e08d3;
  }

  &.color-orange .card-container {
    background-color: #ff7800;
  }

  &.color-cyan .card-container {
    background-color: #00c5a7;
  }

  &.color-pink .card-container {
    background-color: #ff4eb3;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .game-card {
    &:not(.type-toast):not(.type-shop) {
      .center .circle .number {
        font-size: 16px;
      }

      .center .circle {
        width: 24px;
        height: 24px;
      }

      .corner {
        font-size: 10px;
      }
    }

    &.type-toast {
      width: 250px;
      height: 342px;

      .center .circle {
        width: 150px;
        height: 150px;

        .number {
          font-size: 100px;
        }
      }

      .corner {
        font-size: 30px;
      }
    }

    &.type-shop {
      width: 100px;
      height: 136px;

      .center .circle {
        width: 65px;
        height: 65px;

        .number {
          font-size: 40px;
        }
      }

      .corner {
        font-size: 16px;
      }
    }
  }
}
</style>
