<template>
  <div v-click-outside="handleClickOutside">
    <van-popover
      v-model:show="showPopover"
      theme="dark"
      placement="bottom-end"
      :actions="list"
      @select="onSelect"
      teleport="body"
      :close-on-click-outside="true"
    >
      <template #reference>
        <div v-if="isShowBtn">
          <img
            v-if="complainType == '1'"
            @click="onClick"
            class="detailImg"
            src="@/assets/images/detail/more.png"
          />
          <img
            v-else-if="complainType == '2'"
            class="videoPlayImg"
            @click="onClick"
            src="@/assets/images/video/more.webp"
          />
        </div>
      </template>
    </van-popover>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { showToast } from 'vant'
const router = useRouter()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
import { checkReportPermission } from '@/api/match'
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  complainType: {
    type: String,
    default: '',
  },
})
const showPopover = ref(false)
const list = ref([
  {
    value: 0,
    text: t('tooltip.report'),
  },
])
const isShowBtn = ref(true)
// 处理点击外部事件
const handleClickOutside = (isOutside, event) => {
  // 当点击外部区域且popover正在显示时，关闭popover
  if (isOutside && showPopover.value) {
    showPopover.value = false
  }
}

onMounted(() => {})

const onClick = (event) => {
  // 阻止事件冒泡，防止点击事件传播到外层元素
  event.stopPropagation()
  showPopover.value = true
}
// 点击
const onSelect = async () => {
  try {
    const res = await checkReportPermission({
      contestId: props.data.id,
    })
    console.log(res)
    if (res.code == 200) {
      const reportData = {
        reportType: 'match',
        complainType: props.complainType,
        reportId: props.data.id,
        name: props.data.userName,
        toComplainUserId: props.data.userIdStr,
      }
      router.push({ path: '/reportOne', query: reportData })
      emitter.emit('enterReport')
    } else {
      showToast(t('toast.alreadyReport'))
    }
  } catch (error) {
    showToast(t('toast.alreadyReport'))
    console.error('举报前确认权限失败:', error)
  }
}
</script>

<style lang="scss" scoped>
:deep(.van-popover__action) {
  width: 100%;
  font-size: 26px;
  color: #e1e1e1;
  // padding: 20px 30px;
}

.dynamicImg {
  width: 38px;
  height: 38px;
}

.detailImg {
  width: 28px;
  height: 28px;
  // padding-left: 22px;
}

.videoPlayImg {
  width: 50px;
  height: 50px;
}
</style>
