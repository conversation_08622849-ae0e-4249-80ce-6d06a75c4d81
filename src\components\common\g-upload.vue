<template>
  <div class="upload">
    <!-- accept="file"  accept="video/* ,image/*"-->
    <!-- :max-size="4 * 1024 * 1024" @oversize="onOversize" -->
    <van-uploader
      v-model="fileList"
      ref="uploadImg"
      :multiple="true"
      :max-count="maxCount"
      :deletable="isDeletable"
      :before-read="beforeRead"
      :after-read="afterRead"
      :disabled="isPermossion"
      @delete="deleteFile"
    >
      <div class="img">
        <span v-if="showMask" @click="handleClick" class="message"></span>
        <img src="@/assets/images/index/addImage.webp" alt="" />
      </div>
    </van-uploader>
    <van-overlay :show="showOverlay" />
  </div>
</template>

<script setup>
import {
  reactive,
  ref,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  computed,
} from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { uploadImage } from '@/api/home.js'
import emitter from '@/utils/mitt.js'
import Compressor from 'compressorjs'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()
const props = defineProps({
  //上传最大数量
  maxCount: {
    type: Number,
    default: 9,
  },
  mode: {
    type: Array,
    default: [],
  },
  data: {
    type: Array,
    default: [],
  },
  uploadType: {
    type: String,
    require: false,
    default: '',
  },
})
const isPermossion = ref(true)
const isDisabled = ref(false)
const uploadImg = ref(null)
const fileList = ref([])
const oldList = ref(0)
const fileLength = ref(0)
const showMask = ref(true)
const uploadedFiles = ref(0)
const showOverlay = ref(false)
const isDeletable = ref(true)
onMounted(() => {
  // fileList.value = props.mode
  console.log(countStore.deviceType)
  if (countStore.deviceType == 'Web') {
    isPermossion.value = false
  } else {
    if (countStore.isCamera) {
      //    有权限
      isPermossion.value = false
    } else {
      window.location.href = 'uniwebview://checkCameraPermission'
    }
  }
  console.log(props.uploadType)
  if (props.uploadType) {
    passImageUrl()
    isDeletable.value = false
  }
  console.log(isDeletable.value)
})
// watch(() => props.mode, countStore.isCamera, ([val, valCamera]) => {
//     // fileList.value.push(src);
//     // if (props.mode.length) {

//     // }
//     // fileList.value = val
//     console.log(valCamera)
// }, { immediate: true })
watch(
  () => countStore.isCamera,
  (val) => {
    // console.log(val)
    isPermossion.value = !countStore.isCamera
  },
  { immediate: true },
)
// 举报页面
emitter.on('enterReport', () => {
  console.log('初始化')
  // 初始化
  fileList.value = []
})
const emits = defineEmits(['chooseFile', 'imgDelete'])
const handleClick = () => {
  console.log('sss')
  // showToast('相机权限' + isPermossion.value)
  if (isPermossion.value) {
    // 先判断是否有权限 开启权限
    window.location.href = 'uniwebview://requestCameraPerssion'
  } else {
    if (showOverlay.value) {
      console.log('hahhahhh')
    } else {
      nextTick(() => {
        // showToast('成功')
        uploadImg.value.chooseFile()
      })
    }
  }
}
emitter.on('isUploadImage', (data) => {
  if (data == 'true') {
    isPermossion.value = false
    nextTick(() => {
      showToast(t('toast.uploadPrompt'))
    })
  }
})
emitter.on('changeImageDisabled', (data) => {
  isDisabled.value = data
})
// 监听到base64url值
const passImageUrl = () => {
  console.log('s')
  // 假设你有一个Base64编码的图片字符串
  const base64Image =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=='
  // 创建一个Blob对象 countStore.baseUrl
  const byteCharacters = atob(countStore.baseUrl.split(',')[1])
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  const blob = new Blob([byteArray], { type: 'image/png' })
  console.log(blob)
  // 创建一个File对象
  const file = new File([blob], 'image.png', { type: 'image/png' })
  console.log(file)
  if (file.size / 1024 / 1024 > 4) {
    return new Promise((resolve, reject) => {
      new Compressor(file, {
        // 压缩质量，0-1之间。数字越小，压缩比越高
        quality: 0.8,
        success(result) {
          console.log(result)
          // 默认返回result是blob类型的文件，可以转成file并返回，交给afterRead钩子进行上传
          let newFile = new File([result], file.name, { type: file.type })
          const sizeInMB = newFile.size / 1024 / 1024 // 转换为 MB
          console.log(sizeInMB) // 输出: 0.97
          //判断大小
          if (sizeInMB > 4) {
            showToast(t('toast.imageShowToast'))
            showOverlay.value = false
            countStore.baseUrl = ''
            reject()
          } else {
            console.log(newFile)
            uploadBaseFile(newFile)
            resolve(newFile)
          }
        },
        error(err) {
          showToast(t('toast.imageShowToast'))
          showOverlay.value = false
          reject(err)
        },
      })
    })
  } else {
    // console.log('SHANG')
    uploadBaseFile(file)
  }
}
// 上传文件的方法
const uploadBaseFile = (newFile) => {
  console.log('开始上传')
  showOverlay.value = true
  const formData = new FormData()
  formData.append('file', newFile)
  // console.log(formData)
  uploadImage(formData)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code == 200) {
        // showToast('上传成功')
        const file = {
          url: res.url, // 设置url为Base64图片数据
        }
        emits('chooseFile', res.url)
        fileList.value.push(file)
      } else {
        showToast(t('toast.uploadImageFail'))
        countStore.baseUrl = ''
      }
      showOverlay.value = false
    })
    .catch((err) => {
      showOverlay.value = false
      // 接口调用失败之后的操作
      console.log(err)
      showToast(t('toast.uploadImageFail'))
      countStore.baseUrl = ''
    })
}
const deleteFile = (file, index) => {
  console.log(index)
  console.log(file, index.index)
  // 删除文件逻辑，可以根据需要进行服务器删除等操
  console.log(fileList.value)
  if (file.status == 'done') {
    emits('imgDelete', index)
  }
  // fileList.value.splice(index, 1);
}
// 文件上传之前对图片进行压缩
const beforeRead = (file) => {
  if (isDisabled.value) {
    showToast(t('toast.videoToast'))
    return false
  } else {
    oldList.value = fileList.value.length
    console.log(oldList.value)
    showOverlay.value = true
    console.log(file)
    return true
  }
}
// 文件读取完成后的钩子
const afterRead = (file) => {
  console.log(file)
  if (file instanceof Array) {
    fileLength.value = file.length
  } else {
    fileLength.value = 1
  }
  console.log(fileLength.value)
  if (file instanceof Array) {
    console.log('duoge')
    return Promise.all(
      file.map((f, index) => {
        if (f.file.size / 1024 / 1024 > 4) {
          return new Promise((resolve, reject) => {
            new Compressor(f.file, {
              quality: 0.8, // 压缩质量，0-1之间。数字越小，压缩比越高
              success(result) {
                let newFile = new File([result], f.file.name, {
                  type: f.file.type,
                })
                const sizeInMB = newFile.size / 1024 / 1024 // 转换为 MB
                console.log(sizeInMB) // 输出: 0.97
                //判断大小
                if (sizeInMB > 4) {
                  fileLength.value--
                  showToast(t('toast.imageShowToast'))
                  fileList.value.splice(oldList.value + index, 1)
                  console.log(fileLength.value)
                  if (fileLength.value) {
                  } else {
                    showOverlay.value = false
                  }
                  reject()
                } else {
                  f.status = 'uploading'
                  // 上传之前压缩
                  uploadFile(newFile, f)
                  resolve(newFile)
                }
              },
              error(err) {
                fileLength.value--
                showToast(t('toast.imageShowToast'))
                fileList.value.splice(oldList.value + index, 1)
                reject(err)
              },
            })
          })
        } else {
          f.status = 'uploading'
          uploadFile(f.file, f)
        }
      }),
    )
  } else {
    console.log(file)
    if (file.file.size / 1024 / 1024 > 4) {
      return new Promise((resolve, reject) => {
        new Compressor(file.file, {
          // 压缩质量，0-1之间。数字越小，压缩比越高
          quality: 0.8,
          success(result) {
            console.log(result)
            // 默认返回result是blob类型的文件，可以转成file并返回，交给afterRead钩子进行上传
            let newFile = new File([result], file.file.name, {
              type: file.file.type,
            })
            const sizeInMB = newFile.size / 1024 / 1024 // 转换为 MB
            console.log(sizeInMB) // 输出: 0.97
            //判断大小
            if (sizeInMB > 4) {
              fileLength.value--
              showToast(t('toast.imageShowToast'))
              fileList.value.splice(fileList.value.length - 1, 1)
              showOverlay.value = false
              reject()
            } else {
              file.status = 'uploading'
              console.log(newFile)
              uploadFile(newFile, file)
              resolve(newFile)
            }
          },
          error(err) {
            fileLength.value--
            showToast(t('toast.imageShowToast'))
            fileList.value.splice(fileList.value.length - 1, 1)
            showOverlay.value = false
            reject(err)
          },
        })
      })
    } else {
      file.status = 'uploading'
      uploadFile(file.file, file)
    }
  }
}
// 上传文件的方法
const uploadFile = (newFile, file) => {
  console.log('开始上传')
  console.log(fileLength.value)
  const formData = new FormData()
  formData.append('file', newFile)
  // console.log(formData)
  uploadImage(formData)
    .then((res) => {
      // 接口调用成功之后的操作
      console.log(res)
      if (res.code == 200) {
        // showToast('上传成功')
        emits('chooseFile', res.url)
        showMask.value = false
        // console.log(file.status)
        console.log(file)
        file.status = 'done'
      } else {
        file.status = 'failed'
        showToast(t('toast.uploadImageFail'))
      }
      uploadedFiles.value++
      console.log(uploadedFiles.value)
      if (uploadedFiles.value === fileLength.value) {
        console.log('上传完成')
        uploadedFiles.value = 0
        showOverlay.value = false
      }
    })
    .catch((err) => {
      uploadedFiles.value++
      if (uploadedFiles.value === fileLength.value) {
        console.log('上传完成')
        uploadedFiles.value = 0
        showOverlay.value = false
      }
      // 接口调用失败之后的操作
      console.log(err)
      file.status = 'failed'
      showToast(t('toast.uploadImageFail'))
    })
}
onUnmounted(() => {
  emitter.off('enterReport')
  emitter.off('cameraPermission')
  emitter.off('isUploadImage')
})
</script>

<style scoped lang="scss">
:deep(.van-uploader__preview-image) {
  width: 160px;
  height: 160px;
}

:deep(.van-uploader__preview) {
  margin: 0 16px 16px 0;
}

:deep(.van-uploader__upload) {
  width: 160px;
  height: 160px;
  margin: 0 16px 16px 0;
}

:deep(.van-uploader__preview-delete--shadow) {
  width: 38px;
  height: 38px;
  border-radius: 50%;
}

:deep(.van-uploader__preview-delete-icon) {
  top: 1px;
  right: 3px;
  font-size: 38px;
  color: #fff;
}

.upload {
  margin-left: 3px;

  .img {
    width: 160px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #3d556c;
    position: relative;

    img {
      width: 50px;
      height: 50px;
    }

    .message {
      position: absolute;
      top: 0;
      left: 0;
      width: 160px;
      height: 160px;
      z-index: 111;
    }
  }
}

:deep(.van-icon-close:before) {
  /* 移除伪元素内容 */
  content: none;
  /* 设置背景图片 */
}
:deep(.van-uploader__mask-icon) {
  background-image: url('@/assets/images/index/deleteInfo.png');
  background-size: cover;
  /* 根据需要调整背景图片的大小 */
  background-repeat: no-repeat;
  /* 防止背景图片重复 */
  background-position: center;
  /* 将背景图片居中 */
  /* 设置图标的大小 */
  width: 28px;
  /* 根据需要调整宽度 */
  height: 28px;
  /* 根据需要调整高度 */
}
</style>
