import './assets/main.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
// i18n
import i18n from '@/locales'
import popover from '@/components/common/popover.vue'
import 'flag-icons/css/flag-icons.min.css' // 引入国旗css库
// 瀑布流组件
import { VueMasonryPlugin } from 'vue-masonry'
import {
  Button,
  Popup,
  Field,
  Dialog,
  RadioGroup,
  Radio,
  Popover,
  Image as VanImage,
  Uploader,
  Toast,
  Checkbox,
  CheckboxGroup,
  Circle,
  Notify,
  Search,
  Swipe,
  SwipeItem,
  Loading,
  List,
  Lazyload,
  Skeleton,
  SkeletonImage,
  SkeletonParagraph,
  ImagePreview,
  Overlay,
  Icon,
  Sidebar,
  SidebarItem,
  Tab,
  Tabs,
  NavBar,
} from 'vant'
import 'vant/lib/index.css'
import config from '@/utils/env.js'
import clickOutside from 'v3-click-outside'
const app = createApp(App)
app.component('popover', popover)
app.use(createPinia())
app.use(router)
app.use(Button)
app.use(Popup)
app.use(Field)
app.use(Dialog)
app.use(Radio)
app.use(RadioGroup)
app.use(Popover)
app.use(VanImage)
app.use(Uploader)
app.use(Toast)
app.use(Checkbox)
app.use(CheckboxGroup)
app.use(Circle)
app.use(Loading)
app.use(Notify)
app.use(Search)
app.use(Swipe)
app.use(SwipeItem)
app.use(List)
app.use(Lazyload, {
  lazyComponent: true,
})
app.use(Skeleton)
app.use(SkeletonImage)
app.use(SkeletonParagraph)
app.use(ImagePreview)
app.use(Overlay)
app.use(Icon)
app.use(VueMasonryPlugin)
app.use(i18n)
app.use(Sidebar)
app.use(SidebarItem)
app.use(Tab)
app.use(Tabs)
app.use(NavBar)
app.use(clickOutside)
import('thinkingdata-browser')
  .then((module) => {
    const thinkingdata = module.default
    window.ta = thinkingdata
    // 初始化SDK
    ta.init({
      serverUrl: 'https://global-receiver-ta.thinkingdata.cn',
      appId: config.thinkingAppId,
    })
  })
  .catch((error) => {
    console.error('Failed to load thinkingdata:', error)
  })

app.mount('#app')
