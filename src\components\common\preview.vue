<template>
  <van-popup v-model:show="dialogVisible" round @close="close">
    <iframe
      :src="newData(data)"
      sandbox="allow-scripts allow-top-navigation allow-same-origin"
      width="100%"
      height="600px"
    ></iframe>
  </van-popup>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import config from '@/utils/env.js'
// 定义props
const props = defineProps({
  data: {
    type: String,
    require: false,
    default: '',
  },
  dialogFormPreview: {
    type: Boolean,
    require: false,
    default: false,
  },
  type: {
    type: String,
    require: false,
    default: '',
  },
})
// 定义data
const dialogVisible = ref(false)
// const pdf = ref('/api3/gamett/2024/12/13/c3a559f4-f968-4a75-96f4-b44309f2e394.pdf')
// const excel = ref('/api3/gamett/2024/12/13/0706e0eb-1fba-4f37-8a20-97a3de87d43a.xlsx')
// const docx = ref('/api3/gamett/2024/12/13/cf984226-2f77-49f1-9ce1-efb76044a7cd.docx')
// const data = ref('https://fracdn.hapmetasocialltd.com/gamett/2024/12/13/c3a559f4-f968-4a75-96f4-b44309f2e394.pdf')
watch(
  () => props.dialogFormPreview,
  (val) => {
    console.log(val)
    dialogVisible.value = val
  },
  { immediate: true },
)
const emits = defineEmits(['dialogClosePreview'])
const close = () => {
  emits('dialogClosePreview')
}
const newData = (data) => {
  data =
    config.kkfUrl + '?url=' + encodeURIComponent(btoa(config.fileUrl + data))
  console.log(data)
  return data
}
const changePreviewType = () => {
  // event.preventDefault();
}
// mounted
onMounted(() => {
  console.log(props.data)
  // dialogVisible.value = props.dialogFormPreview
})
</script>

<style lang="scss" scoped>
.van-popup {
  width: 85%;
}

.van-popup--center {
  width: 85%;
}

img {
  pointer-events: none;
  // display: none;
}
</style>
