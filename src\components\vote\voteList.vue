<template>
  <div class="listCon">
    <div class="tab">
      <van-tabs
        v-model:active="active"
        type="card"
        shrink
        class="tabCon"
        @change="changeTab"
      >
        <van-tab
          :title="tab.title"
          v-for="(tab, index) in tabs"
          :key="index"
        ></van-tab>
      </van-tabs>
      <img
        class="question"
        @click="openQuestion"
        src="@/assets/images/vote/question.webp"
        alt=""
      />
    </div>

    <div class="voteList">
      <van-list
        class="voteCon"
        @load="changeMsgData"
        :immediate-check="true"
        :offset="1"
      >
        <div v-if="!loading && voteData.length">
          <transition name="fade">
            <div>
              <div v-for="(item, index) in voteData" :key="index">
                <VoteItem
                  :data="item"
                  :index="index"
                  @openVotePopup="openVotePopup"
                >
                </VoteItem>
              </div>
            </div>
          </transition>
        </div>
        <div v-else class="noComment">
          <div class="voteComment">
            <Empty :title="$t('empty.content')" />
          </div>
        </div>
      </van-list>
    </div>
    <VotePopup
      v-if="dialogVotePopup"
      :dialogVotePopup="dialogVotePopup"
      :data="popupData"
      @dialogClose="dialogClose"
      @confirm="confirm"
    >
    </VotePopup>
    <rule
      v-if="dialogRule"
      :dialogRule="dialogRule"
      @dialogClose="dialogCloseRule"
    >
    </rule>
  </div>
</template>
<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  defineAsyncComponent,
  computed,
} from 'vue'
import { showToast } from 'vant'
import { debounce } from 'lodash'
const Empty = defineAsyncComponent(
  () => import('@/components/common/empty.vue'),
)
const VoteItem = defineAsyncComponent(
  () => import('@/components/vote/voteItem.vue'),
)
const VotePopup = defineAsyncComponent(
  () => import('@/components/vote/votePopup.vue'),
)
const skeleton = defineAsyncComponent(
  () => import('@/components/common/skeleton.vue'),
)
const rule = defineAsyncComponent(() => import('@/components/dialog/rule.vue'))
import { voteList, preBetting } from '@/api/home.js'
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import useCounterStore from '@/stores/counter'
const countStore = useCounterStore()

const tabs = ref([
  { id: 1, title: t('vote.voteProgress') },
  { id: 2, title: t('vote.voteEnd') },
  { id: 3, title: t('vote.voteSettle') },
])
const pageNum = ref(1)
const pageSize = ref(10)
const voteData = ref([])
const total = ref(0)
const voteType = ref(1)
const active = ref(0)
const dialogVotePopup = ref(false)
const popupData = ref({})
const popupId = ref(0)
const loading = ref(true)
const dialogRule = ref(false)
// 打开规则
const openQuestion = () => {
  dialogRule.value = true
}
// 关闭规则
const dialogCloseRule = () => {
  dialogRule.value = false
}
// 计算当前 tab 对应的 voteType
const currentVoteType = computed(() => {
  if (active.value === 0) return 1
  if (active.value === 1) return 2
  return 4
})

// 使用防抖处理 tab 切换
const changeTab = debounce((index) => {
  active.value = index
  voteType.value = currentVoteType.value
  loading.value = true
  pageNum.value = 1
  getVoteList()
}, 300)

// 使用防抖处理列表加载
const changeMsgData = debounce(() => {
  if (voteData.value.length < total.value) {
    pageNum.value++
    loading.value = true
    getVoteList()
  }
}, 300)

const getVoteList = (type) => {
  const query = {
    status: voteType.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  voteList(query)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        total.value = res.total
        // 更新缓存
        if (res.rows) {
          if (type == 'vote') {
            // 拿到要修改的id 替换掉
            // popupId.value
            // 遍历新的数据，找到要替换的项
            const itemToUpdate = res.rows.find(
              (item) => item.id === popupId.value,
            )
            if (itemToUpdate) {
              updateListById(itemToUpdate, popupId.value) // 替换原有列表中的数据
            } else {
              console.log('No matching item found in new data')
            }
          } else {
            if (pageNum.value === 1) {
              voteData.value = res.rows
            } else {
              res.rows.forEach((item) => {
                voteData.value.push(item)
              })
            }
          }
        }
      } else {
        voteData.value = []
      }
      loading.value = false
    })
    .catch(function (error) {
      console.log(error)
      loading.value = false
    })
}
const updateListById = (newList, id) => {
  // 假设 voteData 是你的原始列表数据
  const index = voteData.value.findIndex((item) => item.id === id) // 查找具有特定 ID 的项
  if (index !== -1) {
    // 如果找到了，替换该项的数据
    voteData.value[index] = newList // 用新的数据替换
  } else {
    console.log('Item not found')
  }
}
const dialogClose = () => {
  dialogVotePopup.value = false
}

// 投票成功
const confirm = () => {
  dialogVotePopup.value = false
  // pageNum.value = 1;
  // loading.value = true;
  getVoteList('vote')
}

const openVotePopup = debounce(
  (data, id) => {
    popupData.value = data
    popupId.value = id
    getPreBetting(id)
  },
  1000,
  { leading: true, trailing: false },
)
// 是否打开投票
const getPreBetting = (id) => {
  preBetting(id)
    .then((res) => {
      console.log(res)
      if (res.code === 200) {
        dialogVotePopup.value = true
      } else if (res.code === 500) {
        showToast(t('vote.statusEnd'))
      }
    })
    .catch(function (error) {
      console.log(error)
      showToast(t('toast.msgDataListFail'))
    })
}
onMounted(() => {
  getVoteList()
})
</script>
<style lang="scss" scoped>
.listCon {
  height: 100%;
  padding-left: 60px;
  padding-right: 94px;
  display: flex;
  flex-direction: column;

  .tab {
    padding: 40px 0 24px;
    display: flex;
    align-items: center;

    // width: 628px;
    .tabCon {
      flex: 1;

      :deep(.van-tabs__nav--card) {
        margin: 0;
      }

      :deep(.van-tab--card.van-tab--active) {
        background: linear-gradient(138deg, #7cbdf6 0%, #5195e7 100%);
        box-shadow: inset 0px 2px 6px 0px rgba(127, 236, 255, 0.5);
      }

      :deep(.van-tab--active) {
        font-weight: 400;
      }

      :deep(.van-tab--card) {
        background: #8ea8cb;
        color: #ffffff;
        margin-right: 20px;
        border-right: none;
      }

      :deep(.van-tabs__nav) {
        background: none;
      }

      :deep(.van-tabs__nav--card) {
        height: 60px;
      }

      :deep(.van-tabs__wrap) {
        height: 60px;
      }

      :deep(.van-tabs__nav--card) {
        border: none;
        border-radius: 4px;
      }

      :deep(.van-tab__text--ellipsis) {
        overflow: visible;
      }

      :deep(.van-tab) {
        height: 60px;
        font-size: 28px;
        padding: 0 70px;
      }
    }

    .question {
      width: 54px;
      height: 54px;
    }
  }

  .voteList {
    flex: 1;
    overflow: auto;

    .voteCon {
      height: 100%;
      overflow: auto;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    .loading-text {
      margin-top: 10px;
      color: #5195e7;
      font-size: 16px;
    }
  }

  .noComment {
    height: 96%;

    .voteComment {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        308deg,
        #f1f8ff 0%,
        #e3f3ff 46%,
        #f3f7ff 100%
      );
      border-radius: 18px;
      overflow: hidden;
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
