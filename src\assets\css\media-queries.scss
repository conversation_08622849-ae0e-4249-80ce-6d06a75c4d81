/* 竖屏 */
@media screen and (orientation: portrait) {
  // .menu {
  //     width: 456px;
  //     font-size: 56px;
  //     .menuItem {
  //       display: flex;
  //       justify-content: flex-end;
  //       align-items: center;
  //       padding: 54px 62px 58px 60px;
  //       color: #FFFFFF;
  //       font-size: 60px;
  //     }

  //     .tac {
  //       .el-menu-item {
  //         justify-content: flex-end;
  //         height: 160px;
  //         font-size: 56px;
  //         padding-right: 68px;
  //         margin-left: 100px;
  //         margin-right: 26px;
  //       }

  //       .menuItemCon {
  //         .badge {
  //           top: 0;
  //           right: -56px;
  //           width: 60px;
  //           height: 60px;
  //           line-height: 60px;
  //           font-size: 36px;
  //         }
  //       }
  //     }

  //     .add {
  //       bottom: 132px;
  //       right: 0;
  //       width: 358px;
  //       height: 120px;
  //       border-radius: 4px;
  //       font-size: 52px;
  //       line-height: 120px;

  //     }
  //   }
}

/* 横屏 */
@media (orientation: landscape) {
}
