const ko = {
  index: {
    title: '홈페이지 게시물',
    menuItems: {
      Dynamic: '게시물',
      Favorites: '수집',
      Liked: '좋아요',
      Messages: '메시지',
      weChat: '모멘트',
      myDynamic: '내 게시물',
      myFavorites: '내 즐겨찾기',
      myLiked: '내 좋아요',
      Hot: '인기 급상승',
      Recommend: '추천',
      New: '최신',
      Vote: '추측&획득',
      Game: '2048',
    },
    user: {
      praised: '좋아요 얻기',
      collect: '수집',
      chat: '채팅',
      addFriends: '친구 추가',
      friendRequest: '친구 신청',
      verificationMessage: '인증 정보',
      sendNow: '즉시 보내기',
      verificationMessagePlaceholder: '안녕하세요! 만나서 반가워요',
    },
    msg: {
      likeCollectTab: '좋아요 및 즐겨찾기',
      commentAitTab: "댓글 및{'@'}",
      reply: '답장',
      likeCond: '당신의 게시물이 마음에 들었습니다',
      likeConc: '당신의 댓글을 좋아합니다',
      likeConr: '당신의 답장을 좋아합니다 ',
      collectCon: '당신의 게시물을 저장했습니다',
      commentCon: '당신의 게시물에 댓글을 달았습니다',
      replyCon: '답글',
      aitCon: "{'@'}당신",
    },
  },
  detail: {
    replyNum: '총 %num% 개의 답글이 있습니다',
    seeMore: '더 보기',
    stop: '접기',
    originalText: '원본 텍스트',
    translation: '번역',
    commentNum: '개의 댓글',
    send: '보내기',
    reply: '답장',
    replied: '답글을 달았습니다',
    likeListTitle: '%num% 명의 친구로부터 좋아요',
  },
  add: {
    title: '게시물',
    input: '게시물 내용을 입력하세요',
    topic: '주제 추가',
    whoSee: {
      title: '누구에게 보여줄 수 있나요',
      all: '모두',
      friend: '친구가 볼 수 있음',
      oneself: '자신만 볼 수 있음',
    },
    publish: '출시',
    audioStart: '말하기를 클릭하세요',
    audioEnd: '종료하려면 클릭하세요',
    searchPlaceholder: '친구를 검색하세요',
    confirm: '확인',
    audioPermission: '먼저 마이크 권한을 열어주세요',
    imagePermission: '먼저 카메라 권한을 활성화해주세요',
    aitUser: "{'@'}사용자",
  },
  report: {
    next: '다음 단계',
    confirmReport: '신고를 확인하세요',
    placeholder: '내용을 입력하세요',
    detailTitle: '상세 설명',
    imgTitle: '이미지 증거',
    reportSuccess: '신고 성공',
    reportFail: '신고 실패',
    reportSuccessInfo:
      '제출 후, 플랫폼은 적극적으로 검증하고 처리할 것입니다; 질서를 유지해주셔서 감사드립니다!',
    reportPublish: '돌아가기',
    reportOneTitleAfterD: '당신은 %name% 의 게시물을 보고하고 있습니다',
    reportOneTitleAfterC: '당신은 %name% 의 댓글 보고하고 있습니다',
    reportOneTitleAfterR: '당신은 %name% 답변을 보고하고 있습니다',
    reportTwoTitle: '%title%를 선택했습니다',
    reportEndTitleAfterD: '%name%의 게시물에 대한 보고서는 %title%에 속합니다',
    reportEndTitleAfterC: '%name%에 대한 댓글 보고서는 %title%에 속합니다',
    reportEndTitleAfterR: '%name%에 대한 답변 보고서는 %title%에 속합니다',
  },
  tooltip: {
    delete: '삭제',
    modify: '수정',
    cancelCollect: '즐겨찾기 해제',
    report: '보고',
    block: '차단',
  },
  delete: {
    deleteCon: '이 콘텐츠를 삭제하시겠습니까?',
    deleteCancel: '취소',
    deleteConfirm: '확인',
    blockCon: '정말 차단하시겠습니까?',
  },
  toast: {
    likeSuccess: '좋아요 성공',
    likeCancel: '좋아요가 취소되었습니다',
    likeFail: '좋아요 실패',
    collectSuccess: '좋아요 성공',
    collectCancel: '좋아요 취소됨',
    collectFail: '저장 실패',
    publishSuccess: '게시무을 성공적으로 발표됨',
    publishFail: '게시물 발표 실패하셨습니다',
    modifySuccess: '수정 성공',
    topicInfo: '최대 5개의 주제를 선택할 수 있습니다',
    aitInfo: "최대{'@'}5명의 사용자",
    ait: "{'@'}앞에 최소 1자를 입력하세요",
    dynamicInput:
      '동적 콘텐츠, 이미지, 비디오 중에서 최소 하나를 업로드해 주세요',
    nextInfo: '먼저 하나의 옵션을 선택하세요',
    reportSuccess: '신고 성공',
    reportFail: '신고 실패',
    audioTextSuccess: '음성을 텍스트로 변환하는 데 성공했습니다',
    audioTextFail: '음성 인식 실패',
    translationSuccess: '번역 성공',
    translationFail: '번역 실패',
    uploadImageFail: '업로드 실패',
    deleteSuccess: '삭제 성공',
    deleteFail: '삭제 실패',
    // 新加
    imageLimit: '파일 크기는 %num%MB를 초과할 수 없습니다',
    imageNum: '최대 9개의 이미지를 업로드할 수 있습니다',
    uploadPrompt: '사진 & 동영상 업로드를 클릭해주세요',
    filePrompt:
      '(Doc, docx 및 pdf 파일 형식을 지원하며 파일 크기가 5mb를 초과할 수 없음)',
    imageBefore: '단일 이미지는 4mb를 초과하지 않습니다',
    imageShowToast: '업로드 파일이 너무 큽니다',
    audioFail: '녹음 종료 오류',
    collectCancelFail: '취소 실패',
    collectCancelSuccess: '취소 성공',
    dynamicFail: '게시물이 존재하지 않습니다',
    addCommentViolation:
      '제출하신 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다. 수정 후 다시 제출해 주십시오.',
    addCommentFail: '댓글 추가에 실패했습니다',
    addReplyFail: '답글 추가에 실패했습니다',
    addDynamicViolation:
      '제출하신 "게시물" 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
    addTopicViolation:
      '제출하신 "주제"의 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
    addImageViolation:
      '제출하신 "이미지" 콘텐츠가 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
    topicCon: '주제 내용은 비어 있을 수 없습니다',
    getMsgFail: '정보를 가져오는 데 실패했습니다',
    loginFail: '로그인에 실패했습니다',
    aitInfoPermission: '현재 자신만 볼 수 있습니다',
    alreadyReport: '여러 번 신고하셨습니다. 플랫폼 피드백을 기다려 주십시오',
    commentAfterDelete: '의견이 삭제되었습니다',
    replyAfterDelete: '회신이 삭제되었습니다',
    msgDataListFail: '데이터 가져오기 실패',
    videoLimit: '동영상은 25MB를 초과할 수 없습니다.',
    videoPrompt: '최대 1개의 동영상을 업로드할 수 있습니다.',
    videoToast: '이미지 또는 동영상만 업로드 가능합니다',
    imageTitle: '이미지 업로드',
    videoTitle: '동영상 업로드',
    applySuccess: '신청 보내기 성공',
    applyFail: '신청 보내기 실패',
    blacklistPrompt: '블랙리스트에서는 친구를 추가할 수 없습니다',
    friendNumPrompt: '상대방의 친구 수가 가득 찼습니다',
    myNumPrompt: '현재 친구 수가 가득 찼습니다',
    failedPrompt: '파라미터 오류',
    alignPrompt: '이미 상대방을 친구로 추가했으므로 다시 신청할 수 없습니다',
    applyMyPrompt: '자신을 추가할 수 없습니다',
    alignApply:
      '이미 친구 요청을 보냈습니다. 48시간 후에 다시 요청할 수 있습니다',
    blockSuccess: '사용자가 블랙리스트에 추가되었습니다',
    blockFail: '차단에 실패했습니다',
    blockListFull: '차단 목록이 가득 찼습니다',
    checkAgreementPrompt:
      '귀하가《컨텐츠 게시 선언 》에 동의하지 않아 동태를 게시할 수 없습니다',
    AgreementFile: '해당 문서를 읽고 동의했습니다',
    fileTitle: '《컨텐츠 게시 선언 》',
    sameLanguagePrompt: '현재 같은 언어를 사용 중이므로 번역할 필요가 없습니다',
  },
  vote: {
    voteProgress: '진행 중',
    voteEnd: '종료되었습니다',
    voteSettle: '정산 완료',
    oneselfNum: '투표 완료',
    voteNum: '{num} 코인',
    timeName: '남은 시간',
    allNum: '총 코인 수',
    participateInVoting: '총 플레이어 수:',
    getCoins: '이번에 {num} 코인을 획득하셨습니다',
    voteBtn: '선택',
    voteTitle: '{num}에 투표하기',
    inputInfo: '수량을 선택해 주세요',
    voteConfirm: '확인',
    voteSuccess: '성공',
    voteFail: '실패',
    statusEnd: '이벤트가 종료되었습니다',
    voteTnfo: '이벤트에 참여하기 위해 필요한 최소 코인 수는 1입니다',
    hold: '보유',
    balance: '현재 계정 잔액이 부족합니다. 빠른 시일 내에 충전해 주세요',
    remainingTimeData: '{days}일 {hours}시 {minutes}분 {seconds}초',
    remainingTimeDaysHours: '{days}일 {hours}시',
    questionInfo:
      '사용자의 모든 코인은 이번 이벤트의 상금 풀로 들어가며, 정답을 맞춘 사용자는 그들이 추측한 코인의 수에 따라 상금 풀 내의 모든 【타임-스페이스 코인】을 나누게 됩니다.',
  },
  video: {
    videoIndex: '뭔가 말해보세요...',
    videoDetail: '상세 페이지',
    videoTitle: '비디오',
  },
  match: {
    statusProgress: '진행 중',
    statusEnd: '종료되었습니다',
    matchCon: '경쟁 내용',
    matchRule: '경쟁 규칙',
    matchAward: '경쟁 보상',
    matchInstructions: '경쟁 가이드라인',
    matchWorks: '생산',
    matchTitle: '경쟁',
    matchTime: '등록 기간',
    myMatch: '내 경쟁',
    statusWaiting: '시작 대기',
    vote: '투표',
    voted: '투표 완료',
    voteNum: '{num}표',
    uploadWorks: '당신의 작품을 업로드하세요',
    matchAddTitle: '제목',
    titlePlaceholder: '제목을 입력해 주세요',
    submit: '제출',
    content: '콘텐츠',
    voteConfirm: '{name}의 작품에 투표하시겠습니까?',
    voteSuccess: '투표 성공',
    voteFail: '투표 실패',
    imageShowToast: '제목과 이미지는 비울 수 없습니다',
    videoShowToast: '제목과 비디오는 비울 수 없습니다',
    addWorkTitle:
      '제출하신 "제목"의 내용이 규정 위반으로 의심됩니다. 수정 후 다시 제출해 주세요.',
    addWorkContent:
      '제출하신 "내용"의 내용이 규정 위반으로 의심됩니다. 수정 후 다시 제출해 주세요.',
    againAddWork:
      '당신은 이미 현재 대회에 참가했으므로 다시 참가할 수 없습니다!',
    myMatchTitle: '내 작품',
    otherMatchTitle: '다른 사람의 작품',
  },
  empty: {
    comment: '아직 댓글이 없습니다',
    list: '사용 가능한 데이터가 없습니다',
    content: '사용 가능한 콘텐츠가 없습니다',
    message: '메시지가 없습니다',
  },
  game:{
    myCardTitle:`내 카드`,
    merge:`합성`,
    maskObtaining:`우승 축하`,
    maskInfo:`서둘러 친구를 추가하여 새로운 카드를 합성하세요!`,
    clickAnywhere:`계속하려면 아무 곳이나 클릭하십시오`,
    mergeSuccess:`합성 성공`,
    newFriends:`새 친구`,
    receiveTitle:`축하합니다!`,
    receiveInfo:`청구에 성공한 후에는 2048이 사라집니다!`,
    receiveImgText:`텔레포트 카드`,
    receivePublish:`보상을 청구하려면 우편함으로 이동하십시오`,
    shop:`쇼핑몰`,
    purchaseCards:`구매 카드`,
    totalAmount:`총액`,
    purchase:`구매`,
    selectCards:`선택 카드`,
    purchaseSuccessful:`구매 성공`,
    purchaseFailed:`구입 하지 못 했습니다`,
    synthesisFailed:`합성에 실패 했습니다`,
    claimSuccessful:`받기 성공`,
    claimFailed:`주장에 실패 했습니다`,
    exchangeTips:`2048 카드!그것을 텔레포트 카드로 구입하십시오!`,
    rule:`게임 규칙`,
    ruleNum1:`1.게임을 시작하면 색상이 있는 2의 기초카드 카드를 랜덤으로 획득합니다`,
    ruleNum2:`2.같은 숫자, 다른 색깔의 카드판을 가진 다른 사용자와 친구를 추가하면 더욱 고급스러운 카드판을 합성할 수 있다`,
    ruleNum3:`3.합성 성공 후, 두 계정의 원래 숫자의 카드 카드가 두 배의 숫자의 새로운 카드 카드로 업그레이드`,
    ruleNum4:`4.카드의 숫자가 2048이 되면, 당신은 전송카드 1장을 교환할 수 있습니다`,
    ruleNum5:`5.합성에 실패한 원인은 다음과 같다. 업그레이드된 카드 색깔을 상대방과 동색으로 선택하십시오.카드는 이미 다른 사람이 사용한다;당신과 상대방은 이미 친구 관계입니다.당신의 친구 수가 이미 상한에 도달했습니다.상대방 친구 수가 이미 상한에 도달했습니다.`
  }
}

export default ko
