export function getDeviceType() {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  // 检查是否是 iPad
  if (/iPad/i.test(userAgent)) {
    return 'iPad'
  }
  // 检查是否是移动设备
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      userAgent,
    )
  if (isMobile) {
    return 'Mobile'
  }
  // 如果不是移动设备，则认为是 Web 浏览器
  return 'Web'
}
export function getAppType() {
  if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
    // 当前环境是 iOS
    return 'ios'
  } else {
    // 当前环境不是 iOS
    return 'android'
  }
}
