.con {
  padding: 26px 30px;
  overflow: auto;

  .top {
    font-weight: var(--weight5);
    font-size: var(--size_28);
    color: var(--mainTtileColor);
    line-height: 40px;
    margin-bottom: 16px;
    display: flex;

    .title {
      flex: 1;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }

  .searchImg {
    width: 40px;
    height: 40px;
  }

  .groupbox {
    height: 300px;
    overflow: auto;

    .item {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f2f2f2;
      padding: 14px 0;

      .name {
        flex: 1;
        margin-left: 16px;
        font-size: var(--size_22);
        color: var(--bearTextColor);
      }

      :deep(.van-radio__icon) {
        height: 30px;
        width: 30px;
      }

      :deep(.van-icon) {
        height: 30px;
        width: 30px;
      }

      /* 如果你想改变选中状态下的图标大小 */
      :deep(.van-radio__icon .van-icon) {
        font-size: 25px;
        /* 设置背景大小 */
      }
    }
  }

  .labelMore {
    display: flex;
    align-items: center;

    img {
      margin-left: 20px;
      width: 38px;
      height: 38px;
    }
  }

  .publish {
    display: flex;
    justify-content: flex-end;

    .btn {
      // float: right;
      margin: 32px 0 15px;
      // width: 152px;
      height: 62px;
      display: flex;
      align-items: center;
      padding: 0 50px;
      text-align: center;
      color: #c4e5ff;
      font-size: var(--size_28);
      font-weight: var(--weight5);
      background: url('@/assets/images/index/publish.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
