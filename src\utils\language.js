export function getLanguage(data) {
  switch (data) {
    case 'bn':
      return 'Bengali'
      break
    case 'zho':
      return 'ChineseSimplified'
      break
    case 'nl':
      return 'Dutch'
      break
    case 'eng':
      return 'English'
      break
    case 'fr':
      return 'French'
      break
    case 'ge':
      return 'German'
      break
    case 'fa':
      return 'Persian'
      break
    case 'he':
      return 'Hebrew'
      break
    case 'hi':
      return 'Hindi'
      break
    case 'hu':
      return 'Hungarian'
      break
    case 'id':
      return 'Indonesian'
      break
    case 'it':
      return 'Italian'
      break
    case 'jpn':
      return 'Japanese'
      break
    case 'ko':
      return 'Korean'
      break
    case 'sp':
      return 'Spanish'
      break
    case 'pt':
      return 'Portuguese'
      break
    case 'ru':
      return 'Russian'
      break
    case 'tr':
      return 'Turkish'
      break
    case 'ur':
      return 'Urdu'
      break
    case 'vi':
      return 'Vietnamese'
      break
    default:
      return 'English'
      break
  }
}
