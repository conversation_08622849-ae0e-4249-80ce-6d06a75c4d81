// 负责管理 Match 相关的数据加载、缓存和状态
import { ref, reactive } from 'vue'
import { getMatchTitle, getMatchList, myMatchList } from '@/api/match'

// 缓存数据
const state = reactive({
  // 顶部标签数据
  tabs: [],
  // 比赛列表数据 (按分类和状态缓存)
  matchLists: {},
  // 我的比赛列表数据 (按状态缓存)
  myMatchLists: {},
  // 加载状态
  loading: {
    tabs: false,
    lists: {},
    myLists: {},
  },
  // 当前选中的标签
  currentMainTab: null,
  currentSubTab: 1, // 默认"进行中"
  // 是否已初始化
  initialized: false,
})

// 获取顶部标签数据
const fetchMatchTabs = async (force = false) => {
  // 如果已有数据且不强制刷新，则直接返回缓存数据
  if (state.tabs.length > 0 && !force) {
    return state.tabs
  }

  // 设置加载状态
  state.loading.tabs = true

  try {
    const res = await getMatchTitle()
    if (res.code === 200 && res.rows) {
      state.tabs = res.rows

      // 设置默认选中的标签
      if (res.rows.length > 0 && !state.currentMainTab) {
        state.currentMainTab = res.rows[0].id
      }

      return res.rows
    }
    return []
  } catch (error) {
    console.error('获取赛事标签失败:', error)
    return []
  } finally {
    state.loading.tabs = false
  }
}

// 获取比赛列表数据
const fetchMatchList = async (
  categoryId,
  status,
  pageNum = 1,
  pageSize = 10,
  force = false,
) => {
  const cacheKey = `${categoryId}-${status}`

  // 设置该类别和状态的加载状态
  if (!state.loading.lists[cacheKey]) {
    state.loading.lists[cacheKey] = false
  }

  // 如果已有数据且不是加载更多且不强制刷新，则直接返回缓存数据
  if (state.matchLists[cacheKey] && pageNum === 1 && !force) {
    return {
      list: state.matchLists[cacheKey],
      finished: state.matchLists[cacheKey].length < pageSize,
    }
  }

  // 设置加载状态
  state.loading.lists[cacheKey] = true

  try {
    const params = {
      categoryId,
      status,
      pageNum,
      pageSize,
    }

    const res = await getMatchList(params)

    if (res.code === 200) {
      // 如果是第一页或缓存中没有该类别的数据，则替换数据
      if (pageNum === 1 || !state.matchLists[cacheKey]) {
        state.matchLists[cacheKey] = res.rows || []
      } else {
        // 否则追加数据
        state.matchLists[cacheKey] = [
          ...state.matchLists[cacheKey],
          ...(res.rows || []),
        ]
      }

      return {
        list: state.matchLists[cacheKey],
        finished: !res.rows || res.rows.length < pageSize,
      }
    }

    return {
      list: state.matchLists[cacheKey] || [],
      finished: true,
    }
  } catch (error) {
    console.error('获取比赛列表失败:', error)
    return {
      list: state.matchLists[cacheKey] || [],
      finished: true,
      error,
    }
  } finally {
    state.loading.lists[cacheKey] = false
  }
}

// 获取我的比赛列表数据
const fetchMyMatchList = async (
  status,
  pageNum = 1,
  pageSize = 10,
  force = false,
) => {
  const cacheKey = `my-${status}`

  // 设置该状态的加载状态
  if (!state.loading.myLists[cacheKey]) {
    state.loading.myLists[cacheKey] = false
  }

  // 如果已有数据且不是加载更多且不强制刷新，则直接返回缓存数据
  if (state.myMatchLists[cacheKey] && pageNum === 1 && !force) {
    return {
      list: state.myMatchLists[cacheKey],
      finished: state.myMatchLists[cacheKey].length < pageSize,
    }
  }

  // 设置加载状态
  state.loading.myLists[cacheKey] = true

  try {
    const params = {
      status,
      pageNum,
      pageSize,
    }

    const res = await myMatchList(params)

    if (res.code === 200) {
      // 如果是第一页或缓存中没有该状态的数据，则替换数据
      if (pageNum === 1 || !state.myMatchLists[cacheKey]) {
        state.myMatchLists[cacheKey] = res.rows || []
      } else {
        // 否则追加数据
        state.myMatchLists[cacheKey] = [
          ...state.myMatchLists[cacheKey],
          ...(res.rows || []),
        ]
      }

      return {
        list: state.myMatchLists[cacheKey],
        finished: !res.rows || res.rows.length < pageSize,
      }
    }

    return {
      list: state.myMatchLists[cacheKey] || [],
      finished: true,
    }
  } catch (error) {
    console.error('获取我的比赛列表失败:', error)
    return {
      list: state.myMatchLists[cacheKey] || [],
      finished: true,
      error,
    }
  } finally {
    state.loading.myLists[cacheKey] = false
  }
}

// 预加载默认数据
const preloadMatchData = async () => {
  if (state.initialized) return

  try {
    // 并行加载标签和默认列表数据
    const tabsPromise = fetchMatchTabs()

    // 等待标签数据加载完成
    const tabs = await tabsPromise

    if (tabs.length > 0) {
      // 使用第一个标签和默认状态(进行中)预加载列表数据
      const defaultCategoryId = tabs[0].id
      const defaultStatus = 2 // 进行中

      // 预加载默认列表数据
      await fetchMatchList(defaultCategoryId, defaultStatus)
    }

    state.initialized = true
  } catch (error) {
    console.error('预加载Match数据失败:', error)
  }
}

// 设置当前选中的标签
const setCurrentTabs = (mainTab, subTab) => {
  if (mainTab !== undefined) state.currentMainTab = mainTab
  if (subTab !== undefined) state.currentSubTab = subTab
}

// 获取当前状态
const getCurrentState = () => {
  return {
    tabs: state.tabs,
    currentMainTab: state.currentMainTab,
    currentSubTab: state.currentSubTab,
    loading: state.loading,
  }
}

// 清除缓存数据
const clearCache = () => {
  state.matchLists = {}
  state.myMatchLists = {}
}

export default {
  state,
  fetchMatchTabs,
  fetchMatchList,
  fetchMyMatchList,
  preloadMatchData,
  setCurrentTabs,
  getCurrentState,
  clearCache,
}
